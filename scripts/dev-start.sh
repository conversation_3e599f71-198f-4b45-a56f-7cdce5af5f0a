#!/bin/bash

# 时间同步系统开发环境启动脚本
# 
# 功能：
# - 启动后端开发服务器
# - 启动前端开发服务器
# - 实时监控日志
# - 支持热重载

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_backend() {
    echo -e "${PURPLE}[BACKEND]${NC} $1"
}

log_frontend() {
    echo -e "${CYAN}[FRONTEND]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查开发环境依赖..."
    
    # 检查 Rust
    if ! command -v cargo &> /dev/null; then
        log_error "Cargo 未安装，请先安装 Rust"
        exit 1
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建开发配置
create_dev_config() {
    log_info "创建开发环境配置..."
    
    # 创建后端开发配置
    if [[ ! -f "dev-config.toml" ]]; then
        cat > dev-config.toml << EOF
# 开发环境配置

[server]
host = "127.0.0.1"
port = 3000
workers = 2

[hardware]
primary_source = "system"
backup_sources = ["ntp"]
pps_device = "/dev/null"
serial_device = "/dev/null"
serial_baud_rate = 9600

[network]
ntp_port = 1123  # 非特权端口
ptp_port = 1319  # 非特权端口
allowed_networks = ["127.0.0.1/32", "::1/128"]

[system]
log_level = "debug"
data_dir = "./dev-data"
config_file = "./dev-config.toml"

[development]
hot_reload = true
cors_enabled = true
debug_endpoints = true
mock_hardware = true
EOF
        log_success "后端开发配置已创建: dev-config.toml"
    fi
    
    # 创建前端开发环境变量
    if [[ ! -f "frontend/.env.development" ]]; then
        cat > frontend/.env.development << EOF
# 开发环境变量
VITE_API_BASE_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:3000/ws/realtime
VITE_DEV_TOOLS=true
VITE_VERBOSE_LOGGING=true
VITE_ENABLE_MOCK_DATA=true
VITE_HOT_RELOAD=true
EOF
        log_success "前端开发环境变量已创建: frontend/.env.development"
    fi
    
    # 创建数据目录
    mkdir -p dev-data
}

# 安装前端依赖
install_frontend_deps() {
    log_frontend "安装前端依赖..."
    
    cd frontend
    
    if [[ ! -d "node_modules" ]] || [[ "package.json" -nt "node_modules" ]]; then
        npm install
        log_success "前端依赖安装完成"
    else
        log_info "前端依赖已是最新"
    fi
    
    cd ..
}

# 启动后端服务
start_backend() {
    log_backend "启动后端开发服务器..."
    
    # 设置环境变量
    export RUST_LOG=debug
    export RUST_BACKTRACE=1
    
    # 使用 cargo watch 实现热重载
    if command -v cargo-watch &> /dev/null; then
        log_backend "使用 cargo-watch 启动热重载模式..."
        cargo watch -x "run -- --config dev-config.toml" &
    else
        log_warning "cargo-watch 未安装，使用普通模式启动"
        log_info "提示: 运行 'cargo install cargo-watch' 启用热重载"
        cargo run -- --config dev-config.toml &
    fi
    
    BACKEND_PID=$!
    log_backend "后端服务 PID: $BACKEND_PID"
}

# 启动前端服务
start_frontend() {
    log_frontend "启动前端开发服务器..."
    
    cd frontend
    
    # 启动 Vite 开发服务器
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
    
    log_frontend "前端服务 PID: $FRONTEND_PID"
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待后端服务
    local backend_ready=false
    local frontend_ready=false
    local max_wait=30
    local wait_count=0
    
    while [[ $wait_count -lt $max_wait ]]; do
        # 检查后端
        if ! $backend_ready && curl -s http://localhost:3000/api/health > /dev/null 2>&1; then
            log_success "后端服务已就绪"
            backend_ready=true
        fi
        
        # 检查前端
        if ! $frontend_ready && curl -s http://localhost:5173 > /dev/null 2>&1; then
            log_success "前端服务已就绪"
            frontend_ready=true
        fi
        
        if $backend_ready && $frontend_ready; then
            break
        fi
        
        sleep 1
        ((wait_count++))
        
        if [[ $((wait_count % 5)) -eq 0 ]]; then
            log_info "等待服务启动... ($wait_count/$max_wait)"
        fi
    done
    
    if ! $backend_ready; then
        log_warning "后端服务启动超时"
    fi
    
    if ! $frontend_ready; then
        log_warning "前端服务启动超时"
    fi
}

# 显示服务信息
show_service_info() {
    echo ""
    log_success "🎉 开发环境启动完成！"
    echo ""
    echo "📋 服务信息:"
    echo "  🔧 后端 API:     http://localhost:3000"
    echo "  📚 API 文档:     http://localhost:3000/api/docs"
    echo "  🌐 前端界面:     http://localhost:5173"
    echo "  📡 WebSocket:    ws://localhost:3000/ws/realtime"
    echo ""
    echo "🔧 开发工具:"
    echo "  📊 后端日志:     tail -f dev-data/app.log"
    echo "  🔍 前端调试:     打开浏览器开发者工具"
    echo "  🔄 热重载:       保存文件自动重新加载"
    echo ""
    echo "⌨️  快捷键:"
    echo "  Ctrl+C:         停止所有服务"
    echo "  Ctrl+Z:         暂停服务"
    echo ""
    echo "📝 提示:"
    echo "  - 后端代码修改会自动重新编译"
    echo "  - 前端代码修改会自动刷新浏览器"
    echo "  - 配置文件: dev-config.toml"
    echo "  - 数据目录: ./dev-data/"
    echo ""
}

# 监控服务状态
monitor_services() {
    log_info "开始监控服务状态..."
    
    while true; do
        # 检查后端进程
        if ! kill -0 $BACKEND_PID 2>/dev/null; then
            log_error "后端服务已停止"
            break
        fi
        
        # 检查前端进程
        if ! kill -0 $FRONTEND_PID 2>/dev/null; then
            log_error "前端服务已停止"
            break
        fi
        
        sleep 5
    done
}

# 清理函数
cleanup() {
    log_info "正在停止服务..."
    
    # 停止后端服务
    if [[ -n "$BACKEND_PID" ]]; then
        kill $BACKEND_PID 2>/dev/null || true
        log_backend "后端服务已停止"
    fi
    
    # 停止前端服务
    if [[ -n "$FRONTEND_PID" ]]; then
        kill $FRONTEND_PID 2>/dev/null || true
        log_frontend "前端服务已停止"
    fi
    
    # 清理子进程
    jobs -p | xargs -r kill 2>/dev/null || true
    
    log_success "所有服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 显示帮助信息
show_help() {
    echo "时间同步系统开发环境启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --backend-only 仅启动后端服务"
    echo "  --frontend-only 仅启动前端服务"
    echo "  --no-watch     禁用文件监控"
    echo ""
    echo "示例:"
    echo "  $0                启动完整开发环境"
    echo "  $0 --backend-only 仅启动后端"
    echo "  $0 --frontend-only 仅启动前端"
}

# 主函数
main() {
    local backend_only=false
    local frontend_only=false
    local no_watch=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --backend-only)
                backend_only=true
                shift
                ;;
            --frontend-only)
                frontend_only=true
                shift
                ;;
            --no-watch)
                no_watch=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "🚀 启动时间同步系统开发环境..."
    
    # 检查依赖
    check_dependencies
    
    # 创建配置
    create_dev_config
    
    # 安装前端依赖
    if ! $backend_only; then
        install_frontend_deps
    fi
    
    # 启动服务
    if ! $frontend_only; then
        start_backend
    fi
    
    if ! $backend_only; then
        start_frontend
    fi
    
    # 等待服务启动
    wait_for_services
    
    # 显示信息
    show_service_info
    
    # 监控服务
    if ! $no_watch; then
        monitor_services
    else
        log_info "服务已启动，按 Ctrl+C 停止"
        wait
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
