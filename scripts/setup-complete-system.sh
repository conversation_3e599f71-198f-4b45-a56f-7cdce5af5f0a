#!/bin/bash

# 时间同步系统完整部署脚本
# 
# 功能：
# - 检查系统环境
# - 安装必要依赖
# - 构建后端服务
# - 构建前端应用
# - 配置系统服务
# - 启动完整系统

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        log_error "命令 '$1' 未找到，请先安装"
        return 1
    fi
    return 0
}

# 检查系统环境
check_system_requirements() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_success "检测到 Linux 系统"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        log_success "检测到 macOS 系统"
    else
        log_warning "未知操作系统: $OSTYPE"
    fi
    
    # 检查必要命令
    local required_commands=("curl" "git" "cargo" "node" "npm")
    for cmd in "${required_commands[@]}"; do
        if check_command "$cmd"; then
            log_success "✓ $cmd 已安装"
        else
            log_error "✗ $cmd 未安装"
            return 1
        fi
    done
    
    # 检查 Rust 版本
    local rust_version=$(rustc --version | cut -d' ' -f2)
    log_info "Rust 版本: $rust_version"
    
    # 检查 Node.js 版本
    local node_version=$(node --version)
    log_info "Node.js 版本: $node_version"
    
    log_success "系统环境检查完成"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Ubuntu/Debian
        if command -v apt-get &> /dev/null; then
            log_info "使用 apt-get 安装依赖..."
            sudo apt-get update
            sudo apt-get install -y \
                build-essential \
                pkg-config \
                libssl-dev \
                chrony \
                pps-tools \
                gpsd \
                gpsd-clients
        # CentOS/RHEL/Fedora
        elif command -v yum &> /dev/null; then
            log_info "使用 yum 安装依赖..."
            sudo yum install -y \
                gcc \
                gcc-c++ \
                openssl-devel \
                chrony \
                pps-tools \
                gpsd
        elif command -v dnf &> /dev/null; then
            log_info "使用 dnf 安装依赖..."
            sudo dnf install -y \
                gcc \
                gcc-c++ \
                openssl-devel \
                chrony \
                pps-tools \
                gpsd
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            log_info "使用 Homebrew 安装依赖..."
            brew install openssl pkg-config
        else
            log_warning "未检测到 Homebrew，请手动安装 OpenSSL"
        fi
    fi
    
    log_success "系统依赖安装完成"
}

# 构建后端服务
build_backend() {
    log_info "构建后端服务..."
    
    # 检查 Cargo.toml 是否存在
    if [[ ! -f "Cargo.toml" ]]; then
        log_error "未找到 Cargo.toml 文件"
        return 1
    fi
    
    # 构建发布版本
    log_info "编译 Rust 后端..."
    cargo build --release
    
    # 检查构建结果
    if [[ -f "target/release/atomic-clock" ]]; then
        log_success "后端构建成功"
        
        # 显示二进制文件信息
        local binary_size=$(du -h target/release/atomic-clock | cut -f1)
        log_info "二进制文件大小: $binary_size"
    else
        log_error "后端构建失败"
        return 1
    fi
}

# 构建前端应用
build_frontend() {
    log_info "构建前端应用..."
    
    # 进入前端目录
    cd frontend
    
    # 检查 package.json 是否存在
    if [[ ! -f "package.json" ]]; then
        log_error "未找到 package.json 文件"
        cd ..
        return 1
    fi
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm ci
    
    # 构建生产版本
    log_info "构建前端生产版本..."
    npm run build
    
    # 检查构建结果
    if [[ -d "dist" ]]; then
        log_success "前端构建成功"
        
        # 显示构建统计
        local dist_size=$(du -sh dist | cut -f1)
        log_info "前端构建大小: $dist_size"
    else
        log_error "前端构建失败"
        cd ..
        return 1
    fi
    
    cd ..
}

# 创建配置文件
create_config_files() {
    log_info "创建配置文件..."
    
    # 创建后端配置
    if [[ ! -f "config.toml" ]]; then
        log_info "创建后端配置文件..."
        cat > config.toml << EOF
# 时间同步系统配置文件

[server]
host = "0.0.0.0"
port = 3000
workers = 4

[hardware]
primary_source = "pps"
backup_sources = ["ntp", "system"]
pps_device = "/dev/pps0"
serial_device = "/dev/ttyS0"
serial_baud_rate = 9600

[network]
ntp_port = 123
ptp_port = 319
allowed_networks = ["0.0.0.0/0"]

[system]
log_level = "info"
data_dir = "/var/lib/atomic-clock"
config_file = "/etc/atomic-clock/config.toml"
EOF
        log_success "后端配置文件已创建"
    fi
    
    # 创建前端环境配置
    if [[ ! -f "frontend/.env.local" ]]; then
        log_info "创建前端环境配置..."
        cat > frontend/.env.local << EOF
# 本地环境配置
VITE_API_BASE_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:3000/ws/realtime
VITE_DEV_TOOLS=true
VITE_VERBOSE_LOGGING=true
EOF
        log_success "前端环境配置已创建"
    fi
}

# 创建系统服务
create_systemd_service() {
    log_info "创建 systemd 服务..."
    
    local service_file="/etc/systemd/system/atomic-clock.service"
    local current_dir=$(pwd)
    local binary_path="$current_dir/target/release/atomic-clock"
    local config_path="$current_dir/config.toml"
    
    # 创建服务文件
    sudo tee "$service_file" > /dev/null << EOF
[Unit]
Description=Atomic Clock Time Synchronization Service
Documentation=https://github.com/mantoumaster/atomic-clock
After=network.target
Wants=network.target

[Service]
Type=simple
User=atomic-clock
Group=atomic-clock
ExecStart=$binary_path --config $config_path
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=atomic-clock

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/atomic-clock
CapabilityBoundingSet=CAP_NET_BIND_SERVICE CAP_SYS_TIME

# 环境变量
Environment=RUST_LOG=info
Environment=RUST_BACKTRACE=1

[Install]
WantedBy=multi-user.target
EOF
    
    # 创建用户和组
    if ! id "atomic-clock" &>/dev/null; then
        log_info "创建系统用户..."
        sudo useradd --system --shell /bin/false --home-dir /var/lib/atomic-clock atomic-clock
    fi
    
    # 创建数据目录
    sudo mkdir -p /var/lib/atomic-clock
    sudo chown atomic-clock:atomic-clock /var/lib/atomic-clock
    
    # 重新加载 systemd
    sudo systemctl daemon-reload
    
    log_success "systemd 服务已创建"
}

# 配置 Nginx（可选）
configure_nginx() {
    log_info "配置 Nginx..."
    
    if ! command -v nginx &> /dev/null; then
        log_warning "Nginx 未安装，跳过配置"
        return 0
    fi
    
    local nginx_config="/etc/nginx/sites-available/atomic-clock"
    
    sudo tee "$nginx_config" > /dev/null << EOF
server {
    listen 80;
    server_name localhost;
    
    # 前端静态文件
    location / {
        root $(pwd)/frontend/dist;
        try_files \$uri \$uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # WebSocket 代理
    location /ws/ {
        proxy_pass http://127.0.0.1:3000/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    # 启用站点
    sudo ln -sf "$nginx_config" /etc/nginx/sites-enabled/
    
    # 测试配置
    if sudo nginx -t; then
        log_success "Nginx 配置成功"
        sudo systemctl reload nginx
    else
        log_error "Nginx 配置测试失败"
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启用并启动 atomic-clock 服务
    sudo systemctl enable atomic-clock
    sudo systemctl start atomic-clock
    
    # 检查服务状态
    if sudo systemctl is-active --quiet atomic-clock; then
        log_success "atomic-clock 服务启动成功"
    else
        log_error "atomic-clock 服务启动失败"
        sudo systemctl status atomic-clock
        return 1
    fi
    
    # 显示服务信息
    log_info "服务状态:"
    sudo systemctl status atomic-clock --no-pager -l
}

# 显示完成信息
show_completion_info() {
    log_success "🎉 时间同步系统部署完成！"
    echo ""
    echo "📋 系统信息:"
    echo "  - 后端服务: http://localhost:3000"
    echo "  - API 文档: http://localhost:3000/api/docs"
    echo "  - WebSocket: ws://localhost:3000/ws/realtime"
    
    if command -v nginx &> /dev/null; then
        echo "  - 前端界面: http://localhost"
    else
        echo "  - 前端界面: 请手动部署 frontend/dist 目录"
    fi
    
    echo ""
    echo "🔧 管理命令:"
    echo "  - 查看服务状态: sudo systemctl status atomic-clock"
    echo "  - 重启服务: sudo systemctl restart atomic-clock"
    echo "  - 查看日志: sudo journalctl -u atomic-clock -f"
    echo "  - 停止服务: sudo systemctl stop atomic-clock"
    echo ""
    echo "📚 更多信息请查看项目文档"
}

# 主函数
main() {
    log_info "🚀 开始部署时间同步系统..."
    echo ""
    
    # 检查是否为 root 用户
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以 root 用户运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_system_requirements
    install_system_dependencies
    create_config_files
    build_backend
    build_frontend
    create_systemd_service
    configure_nginx
    start_services
    show_completion_info
    
    log_success "✅ 部署完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
