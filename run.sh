#!/bin/bash

# 时间同步系统统一管理脚本
# 
# 功能：
# - 环境检查和依赖安装
# - 开发环境启动
# - 生产环境构建和部署
# - 服务管理
# - 系统监控

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 全局变量
BACKEND_PID=""
FRONTEND_PID=""
PROJECT_ROOT=$(pwd)

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_backend() { echo -e "${PURPLE}[BACKEND]${NC} $1"; }
log_frontend() { echo -e "${CYAN}[FRONTEND]${NC} $1"; }

# 检查命令是否存在
check_command() {
    command -v "$1" &> /dev/null
}

# 环境检查
check_environment() {
    log_info "检查系统环境..."
    
    # 检查必要命令
    local missing_commands=()
    
    if ! check_command "cargo"; then missing_commands+=("cargo (Rust)"); fi
    if ! check_command "node"; then missing_commands+=("node"); fi
    if ! check_command "npm"; then missing_commands+=("npm"); fi
    if ! check_command "curl"; then missing_commands+=("curl"); fi
    
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "缺少必要命令: ${missing_commands[*]}"
        log_info "请先安装缺少的依赖"
        return 1
    fi
    
    # 显示版本信息
    log_info "Rust: $(rustc --version | cut -d' ' -f2)"
    log_info "Node.js: $(node --version)"
    log_info "npm: $(npm --version)"
    
    log_success "环境检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 安装前端依赖
    log_frontend "安装前端依赖..."
    cd frontend
    npm install
    cd ..
    
    # 检查 Rust 依赖（通过编译检查）
    log_backend "检查后端依赖..."
    cargo check
    
    log_success "依赖安装完成"
}

# 创建开发配置
create_dev_config() {
    log_info "创建开发环境配置..."
    
    # 后端开发配置
    cat > dev-config.toml << 'EOF'
[server]
host = "127.0.0.1"
port = 3000
workers = 2

[hardware]
primary_source = "system"
backup_sources = ["ntp"]
pps_device = "/dev/null"
serial_device = "/dev/null"
serial_baud_rate = 9600

[network]
ntp_port = 1123
ptp_port = 1319
allowed_networks = ["127.0.0.1/32", "::1/128"]

[system]
log_level = "debug"
data_dir = "./dev-data"
config_file = "./dev-config.toml"
EOF
    
    # 前端开发环境变量
    cat > frontend/.env.development << 'EOF'
VITE_API_BASE_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:3000/ws/realtime
VITE_DEV_TOOLS=true
VITE_VERBOSE_LOGGING=true
EOF
    
    # 创建数据目录
    mkdir -p dev-data
    
    log_success "开发配置已创建"
}

# 创建生产配置
create_prod_config() {
    log_info "创建生产环境配置..."
    
    # 生产配置
    cat > config.toml << 'EOF'
[server]
host = "0.0.0.0"
port = 3000
workers = 4

[hardware]
primary_source = "pps"
backup_sources = ["ntp", "system"]
pps_device = "/dev/pps0"
serial_device = "/dev/ttyS0"
serial_baud_rate = 9600

[network]
ntp_port = 123
ptp_port = 319
allowed_networks = ["0.0.0.0/0"]

[system]
log_level = "info"
data_dir = "/var/lib/atomic-clock"
config_file = "/etc/atomic-clock/config.toml"
EOF
    
    log_success "生产配置已创建"
}

# 开发环境启动
start_dev() {
    log_info "🚀 启动开发环境..."
    
    check_environment
    create_dev_config
    
    # 启动后端
    log_backend "启动后端开发服务器..."
    export RUST_LOG=debug
    export RUST_BACKTRACE=1
    
    if check_command "cargo-watch"; then
        cargo watch -x "run -- --config dev-config.toml" &
    else
        log_warning "建议安装 cargo-watch: cargo install cargo-watch"
        cargo run -- --config dev-config.toml &
    fi
    BACKEND_PID=$!
    
    # 启动前端
    log_frontend "启动前端开发服务器..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 5
    
    # 显示服务信息
    echo ""
    log_success "🎉 开发环境启动完成！"
    echo ""
    echo "📋 服务信息:"
    echo "  🔧 后端 API:     http://localhost:3000"
    echo "  📚 API 文档:     http://localhost:3000/api/docs"
    echo "  🌐 前端界面:     http://localhost:5173"
    echo "  📡 WebSocket:    ws://localhost:3000/ws/realtime"
    echo ""
    echo "⌨️  按 Ctrl+C 停止所有服务"
    echo ""
    
    # 等待用户中断
    trap cleanup SIGINT SIGTERM
    wait
}

# 构建生产版本
build_prod() {
    log_info "🏗️ 构建生产版本..."
    
    check_environment
    create_prod_config
    
    # 构建后端
    log_backend "构建后端..."
    cargo build --release
    
    if [[ -f "target/release/atomic-clock" ]]; then
        local size=$(du -h target/release/atomic-clock | cut -f1)
        log_success "后端构建完成，大小: $size"
    else
        log_error "后端构建失败"
        return 1
    fi
    
    # 构建前端
    log_frontend "构建前端..."
    cd frontend
    npm run build
    cd ..
    
    if [[ -d "frontend/dist" ]]; then
        local size=$(du -sh frontend/dist | cut -f1)
        log_success "前端构建完成，大小: $size"
    else
        log_error "前端构建失败"
        return 1
    fi
    
    log_success "✅ 生产版本构建完成！"
    echo ""
    echo "📦 构建产物:"
    echo "  🔧 后端二进制: target/release/atomic-clock"
    echo "  🌐 前端静态文件: frontend/dist/"
    echo "  ⚙️ 配置文件: config.toml"
}

# 部署到系统
deploy_system() {
    log_info "🚀 部署到系统..."
    
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以 root 用户运行部署"
        exit 1
    fi
    
    # 先构建
    build_prod
    
    # 创建系统用户
    if ! id "atomic-clock" &>/dev/null; then
        log_info "创建系统用户..."
        sudo useradd --system --shell /bin/false --home-dir /var/lib/atomic-clock atomic-clock
    fi
    
    # 创建目录
    sudo mkdir -p /var/lib/atomic-clock
    sudo mkdir -p /etc/atomic-clock
    sudo chown atomic-clock:atomic-clock /var/lib/atomic-clock
    
    # 复制文件
    log_info "复制文件..."
    sudo cp target/release/atomic-clock /usr/local/bin/
    sudo cp config.toml /etc/atomic-clock/
    sudo chmod +x /usr/local/bin/atomic-clock
    
    # 创建 systemd 服务
    log_info "创建系统服务..."
    sudo tee /etc/systemd/system/atomic-clock.service > /dev/null << 'EOF'
[Unit]
Description=Atomic Clock Time Synchronization Service
After=network.target

[Service]
Type=simple
User=atomic-clock
Group=atomic-clock
ExecStart=/usr/local/bin/atomic-clock --config /etc/atomic-clock/config.toml
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 启动服务
    sudo systemctl daemon-reload
    sudo systemctl enable atomic-clock
    sudo systemctl start atomic-clock
    
    log_success "✅ 系统部署完成！"
    echo ""
    echo "🔧 管理命令:"
    echo "  sudo systemctl status atomic-clock    # 查看状态"
    echo "  sudo systemctl restart atomic-clock   # 重启服务"
    echo "  sudo journalctl -u atomic-clock -f    # 查看日志"
}

# 服务管理
manage_service() {
    local action=$1
    
    case $action in
        status)
            sudo systemctl status atomic-clock
            ;;
        start)
            sudo systemctl start atomic-clock
            log_success "服务已启动"
            ;;
        stop)
            sudo systemctl stop atomic-clock
            log_success "服务已停止"
            ;;
        restart)
            sudo systemctl restart atomic-clock
            log_success "服务已重启"
            ;;
        logs)
            sudo journalctl -u atomic-clock -f
            ;;
        *)
            log_error "未知操作: $action"
            echo "可用操作: status, start, stop, restart, logs"
            ;;
    esac
}

# 清理函数
cleanup() {
    log_info "正在停止服务..."
    
    if [[ -n "$BACKEND_PID" ]]; then
        kill $BACKEND_PID 2>/dev/null || true
        log_backend "后端服务已停止"
    fi
    
    if [[ -n "$FRONTEND_PID" ]]; then
        kill $FRONTEND_PID 2>/dev/null || true
        log_frontend "前端服务已停止"
    fi
    
    jobs -p | xargs -r kill 2>/dev/null || true
    log_success "所有服务已停止"
    exit 0
}

# 显示帮助
show_help() {
    echo "时间同步系统统一管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  setup          检查环境并安装依赖"
    echo "  dev            启动开发环境"
    echo "  build          构建生产版本"
    echo "  deploy         部署到系统"
    echo "  service <op>   管理系统服务 (status|start|stop|restart|logs)"
    echo "  clean          清理构建文件"
    echo "  help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 setup              # 初始化环境"
    echo "  $0 dev                # 启动开发环境"
    echo "  $0 build              # 构建生产版本"
    echo "  $0 deploy             # 部署到系统"
    echo "  $0 service status     # 查看服务状态"
    echo "  $0 service logs       # 查看服务日志"
}

# 清理构建文件
clean_build() {
    log_info "清理构建文件..."
    
    # 清理 Rust 构建文件
    cargo clean
    
    # 清理前端构建文件
    rm -rf frontend/dist
    rm -rf frontend/node_modules/.vite
    
    # 清理配置文件
    rm -f dev-config.toml
    rm -f config.toml
    rm -rf dev-data
    
    log_success "清理完成"
}

# 主函数
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi
    
    local command=$1
    shift
    
    case $command in
        setup)
            check_environment
            install_dependencies
            ;;
        dev)
            start_dev
            ;;
        build)
            build_prod
            ;;
        deploy)
            deploy_system
            ;;
        service)
            if [[ $# -eq 0 ]]; then
                log_error "请指定服务操作"
                echo "可用操作: status, start, stop, restart, logs"
                exit 1
            fi
            manage_service "$1"
            ;;
        clean)
            clean_build
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
