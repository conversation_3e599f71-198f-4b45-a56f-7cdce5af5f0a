/**
 * 应用程序入口文件
 *
 * 功能实现：
 * - Vue 应用创建和配置
 * - Pinia 状态管理初始化
 * - 全局样式和字体导入
 * - 应用挂载和错误处理
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

// 导入样式文件
import './styles/main.css'

// 导入字体文件
import '@fontsource/inter/400.css'
import '@fontsource/inter/500.css'
import '@fontsource/inter/600.css'
import '@fontsource/inter/700.css'

// 创建 Vue 应用实例
const app = createApp(App)

// 创建并安装 Pinia 状态管理
const pinia = createPinia()
app.use(pinia)

// 全局错误处理器
app.config.errorHandler = (error, instance, info) => {
  console.error('❌ Vue 应用错误:', error)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)

  // 在生产环境中，可以将错误发送到监控服务
  if (import.meta.env.PROD) {
    // 例如：sendErrorToMonitoring(error, info)
  }
}

// 全局警告处理器（仅在开发环境）
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('⚠️ Vue 警告:', msg)
    console.warn('组件追踪:', trace)
  }
}

// 挂载应用到 DOM
app.mount('#app')

// 输出启动信息
console.log('🚀 时间同步系统前端应用已启动')
console.log('📦 环境信息:', {
  mode: import.meta.env.MODE,
  dev: import.meta.env.DEV,
  prod: import.meta.env.PROD,
  apiUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:3000/ws/realtime'
})
