<!--
  主仪表板组件
  
  功能特性：
  - 系统状态概览
  - 实时数据监控
  - 时间源状态显示
  - 告警通知
  - 性能指标图表
  - 响应式布局设计
-->
<template>
  <div class="dashboard">
    <!-- 页面标题栏 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="dashboard-title">
          <span class="title-icon">⏰</span>
          时间同步系统监控台
        </h1>
        
        <!-- 连接状态指示器 -->
        <div class="connection-status">
          <div 
            :class="[
              'status-indicator',
              {
                'status-connected': realtimeStore.isConnected,
                'status-connecting': realtimeStore.isConnecting,
                'status-error': realtimeStore.hasConnectionError
              }
            ]"
          >
            <span class="status-dot"></span>
            <span class="status-text">
              {{ connectionStatusText }}
            </span>
          </div>
          
          <!-- 最后更新时间 -->
          <div v-if="realtimeStore.dataStats.lastDataReceived" class="last-update">
            最后更新: {{ formatTime(realtimeStore.dataStats.lastDataReceived) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 系统状态卡片组 -->
      <div class="status-cards">
        <!-- 系统健康状态 -->
        <div class="status-card">
          <div class="card-header">
            <h3 class="card-title">系统状态</h3>
            <div :class="['health-badge', `health-${systemStore.overallHealth.toLowerCase()}`]">
              {{ healthStatusText(systemStore.overallHealth) }}
            </div>
          </div>
          
          <div class="card-content">
            <div class="status-item">
              <span class="status-label">运行时间</span>
              <span class="status-value">{{ systemStore.formattedUptime }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">活跃服务</span>
              <span class="status-value">
                {{ systemStore.runningServicesCount }} / {{ systemStore.totalServicesCount }}
              </span>
            </div>
            
            <div class="status-item">
              <span class="status-label">连接质量</span>
              <span class="status-value">{{ realtimeStore.connectionQuality }}%</span>
            </div>
          </div>
        </div>

        <!-- 时间源状态 -->
        <div class="status-card">
          <div class="card-header">
            <h3 class="card-title">时间源</h3>
            <GlassButton 
              size="sm" 
              variant="ghost"
              @click="refreshTimeSources"
              :loading="timeSourcesStore.loading.list"
            >
              <template #icon>🔄</template>
              刷新
            </GlassButton>
          </div>
          
          <div class="card-content">
            <div class="status-item">
              <span class="status-label">活跃源</span>
              <span class="status-value">{{ timeSourcesStore.activeTimeSourcesCount }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">健康源</span>
              <span class="status-value">{{ timeSourcesStore.healthyTimeSourcesCount }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">平均质量</span>
              <span class="status-value">{{ (timeSourcesStore.averageQualityScore * 100).toFixed(1) }}%</span>
            </div>
          </div>
        </div>

        <!-- 实时数据统计 -->
        <div class="status-card">
          <div class="card-header">
            <h3 class="card-title">数据统计</h3>
          </div>
          
          <div class="card-content">
            <div class="status-item">
              <span class="status-label">时间戳</span>
              <span class="status-value">{{ realtimeStore.dataStats.timestampCount }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">性能指标</span>
              <span class="status-value">{{ realtimeStore.dataStats.metricsCount }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">告警数量</span>
              <span class="status-value">{{ realtimeStore.dataStats.alertCount }}</span>
            </div>
          </div>
        </div>

        <!-- 告警通知 -->
        <div class="status-card">
          <div class="card-header">
            <h3 class="card-title">告警通知</h3>
            <div v-if="realtimeStore.unreadAlertsCount > 0" class="alert-badge">
              {{ realtimeStore.unreadAlertsCount }}
            </div>
          </div>
          
          <div class="card-content">
            <div class="status-item">
              <span class="status-label">未读告警</span>
              <span class="status-value">{{ realtimeStore.unreadAlertsCount }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">未确认</span>
              <span class="status-value">{{ realtimeStore.unacknowledgedAlertsCount }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">危险告警</span>
              <span class="status-value">{{ realtimeStore.alertsByLevel.critical.length }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时数据显示区域 -->
      <div class="realtime-section">
        <!-- 当前时间戳 -->
        <div class="realtime-card">
          <div class="card-header">
            <h3 class="card-title">当前时间戳</h3>
          </div>
          
          <div class="card-content">
            <div v-if="realtimeStore.latestTimestamp" class="timestamp-display">
              <div class="timestamp-main">
                {{ formatTimestamp(realtimeStore.latestTimestamp) }}
              </div>
              
              <div class="timestamp-details">
                <div class="detail-item">
                  <span class="detail-label">精度:</span>
                  <span class="detail-value">{{ realtimeStore.latestTimestamp.precision }}s</span>
                </div>
                
                <div class="detail-item">
                  <span class="detail-label">来源:</span>
                  <span class="detail-value">{{ realtimeStore.latestTimestamp.source }}</span>
                </div>
                
                <div class="detail-item">
                  <span class="detail-label">纳秒:</span>
                  <span class="detail-value">{{ realtimeStore.latestTimestamp.nanoseconds }}</span>
                </div>
              </div>
            </div>
            
            <div v-else class="no-data">
              <span class="no-data-icon">📡</span>
              <span class="no-data-text">等待时间戳数据...</span>
            </div>
          </div>
        </div>

        <!-- 性能指标 -->
        <div class="realtime-card">
          <div class="card-header">
            <h3 class="card-title">系统性能</h3>
          </div>
          
          <div class="card-content">
            <div v-if="realtimeStore.latestMetrics" class="metrics-display">
              <div class="metric-item" v-if="realtimeStore.latestMetrics.cpu_usage !== undefined">
                <div class="metric-label">CPU 使用率</div>
                <div class="metric-value">{{ realtimeStore.latestMetrics.cpu_usage.toFixed(1) }}%</div>
                <div class="metric-bar">
                  <div 
                    class="metric-fill" 
                    :style="{ width: `${realtimeStore.latestMetrics.cpu_usage}%` }"
                  ></div>
                </div>
              </div>
              
              <div class="metric-item" v-if="realtimeStore.latestMetrics.memory_usage !== undefined">
                <div class="metric-label">内存使用率</div>
                <div class="metric-value">{{ realtimeStore.latestMetrics.memory_usage.toFixed(1) }}%</div>
                <div class="metric-bar">
                  <div 
                    class="metric-fill" 
                    :style="{ width: `${realtimeStore.latestMetrics.memory_usage}%` }"
                  ></div>
                </div>
              </div>
              
              <div class="metric-item" v-if="realtimeStore.latestMetrics.disk_usage !== undefined">
                <div class="metric-label">磁盘使用率</div>
                <div class="metric-value">{{ realtimeStore.latestMetrics.disk_usage.toFixed(1) }}%</div>
                <div class="metric-bar">
                  <div 
                    class="metric-fill" 
                    :style="{ width: `${realtimeStore.latestMetrics.disk_usage}%` }"
                  ></div>
                </div>
              </div>
            </div>
            
            <div v-else class="no-data">
              <span class="no-data-icon">📊</span>
              <span class="no-data-text">等待性能数据...</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近告警列表 -->
      <div class="alerts-section">
        <div class="section-header">
          <h3 class="section-title">最近告警</h3>
          
          <div class="section-actions">
            <GlassButton 
              size="sm" 
              variant="ghost"
              @click="realtimeStore.markAllAlertsAsRead"
              :disabled="realtimeStore.unreadAlertsCount === 0"
            >
              全部已读
            </GlassButton>
            
            <GlassButton 
              size="sm" 
              variant="ghost"
              @click="realtimeStore.clearAllAlerts"
              :disabled="realtimeStore.alerts.length === 0"
            >
              清除全部
            </GlassButton>
          </div>
        </div>
        
        <div class="alerts-list">
          <div 
            v-for="alert in realtimeStore.recentAlerts" 
            :key="alert.alert_id"
            :class="[
              'alert-item',
              `alert-${alert.level}`,
              { 'alert-unread': !alert.isRead }
            ]"
            @click="handleAlertClick(alert)"
          >
            <div class="alert-icon">
              {{ getAlertIcon(alert.level) }}
            </div>
            
            <div class="alert-content">
              <div class="alert-header">
                <span class="alert-component">{{ alert.component }}</span>
                <span class="alert-time">{{ formatTime(alert.receivedAt) }}</span>
              </div>
              
              <div class="alert-message">{{ alert.message }}</div>
            </div>
            
            <div class="alert-actions">
              <button 
                v-if="!alert.isAcknowledged"
                class="alert-action-btn"
                @click.stop="realtimeStore.acknowledgeAlert(alert.alert_id)"
                title="确认告警"
              >
                ✓
              </button>
              
              <button 
                class="alert-action-btn"
                @click.stop="realtimeStore.removeAlert(alert.alert_id)"
                title="删除告警"
              >
                ✕
              </button>
            </div>
          </div>
          
          <div v-if="realtimeStore.alerts.length === 0" class="no-alerts">
            <span class="no-alerts-icon">🎉</span>
            <span class="no-alerts-text">暂无告警信息</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 主仪表板组件脚本
 * 
 * 功能实现：
 * - 集成多个 Pinia Store 的状态管理
 * - 实时数据展示和更新
 * - 用户交互处理
 * - 数据格式化和显示
 */

import { computed, onMounted, onUnmounted } from 'vue'
import { useSystemStore } from '@/stores/system'
import { useTimeSourcesStore } from '@/stores/timeSources'
import { useRealtimeStore } from '@/stores/realtime'
import GlassButton from '@/components/glass/GlassButton.vue'
import type { AlertData } from '@/stores/realtime'
import type { HealthStatus } from '@/api/types'

// ==================== Store 实例 ====================

/** 系统状态管理 */
const systemStore = useSystemStore()

/** 时间源管理 */
const timeSourcesStore = useTimeSourcesStore()

/** 实时数据管理 */
const realtimeStore = useRealtimeStore()

// ==================== 计算属性 ====================

/** 连接状态文本 */
const connectionStatusText = computed(() => {
  if (realtimeStore.isConnected) return '已连接'
  if (realtimeStore.isConnecting) return '连接中...'
  if (realtimeStore.hasConnectionError) return '连接错误'
  return '未连接'
})

// ==================== 方法定义 ====================

/**
 * 格式化健康状态文本
 */
function healthStatusText(status: HealthStatus): string {
  const statusMap = {
    Healthy: '健康',
    Warning: '警告',
    Critical: '危险',
    Unknown: '未知'
  }
  return statusMap[status] || '未知'
}

/**
 * 格式化时间戳显示
 */
function formatTimestamp(timestamp: any): string {
  const date = new Date(timestamp.timestamp * 1000 + timestamp.nanoseconds / 1000000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  })
}

/**
 * 格式化时间显示
 */
function formatTime(date: Date): string {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 获取告警图标
 */
function getAlertIcon(level: string): string {
  const iconMap = {
    critical: '🔴',
    error: '🟠',
    warning: '🟡',
    info: '🔵'
  }
  return iconMap[level as keyof typeof iconMap] || '⚪'
}

/**
 * 处理告警点击事件
 */
function handleAlertClick(alert: AlertData): void {
  if (!alert.isRead) {
    realtimeStore.markAlertAsRead(alert.alert_id)
  }
}

/**
 * 刷新时间源数据
 */
async function refreshTimeSources(): Promise<void> {
  await timeSourcesStore.refreshTimeSources()
}

/**
 * 初始化数据加载
 */
async function initializeData(): Promise<void> {
  console.log('🚀 初始化仪表板数据')
  
  // 并行加载所有初始数据
  await Promise.all([
    systemStore.refreshAll(),
    timeSourcesStore.fetchTimeSources()
  ])
  
  // 初始化实时数据连接
  realtimeStore.initializeConnection()
}

/**
 * 设置定时刷新
 */
let refreshInterval: number | null = null

function setupAutoRefresh(): void {
  // 每30秒刷新一次系统状态
  refreshInterval = window.setInterval(async () => {
    if (!realtimeStore.isConnected) {
      await systemStore.fetchSystemStatus()
    }
  }, 30000)
}

function clearAutoRefresh(): void {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}

// ==================== 生命周期钩子 ====================

onMounted(async () => {
  await initializeData()
  setupAutoRefresh()
})

onUnmounted(() => {
  clearAutoRefresh()
  realtimeStore.disconnectWebSocket()
})
</script>

<!--
  仪表板样式定义

  设计特色：
  - 苹果风格的毛玻璃效果
  - 响应式网格布局
  - 平滑的动画过渡
  - 现代化的卡片设计
  - 直观的状态指示器
-->
<style scoped>
/* 主容器样式 */
.dashboard {
  @apply min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900;
  @apply text-white overflow-hidden;
}

/* 页面标题栏 */
.dashboard-header {
  @apply glass border-b border-white/10 sticky top-0 z-10;
  backdrop-filter: blur(20px);
}

.header-content {
  @apply max-w-7xl mx-auto px-6 py-4 flex items-center justify-between;
}

.dashboard-title {
  @apply text-2xl font-bold flex items-center gap-3;
}

.title-icon {
  @apply text-3xl;
}

/* 连接状态指示器 */
.connection-status {
  @apply flex items-center gap-4;
}

.status-indicator {
  @apply flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium;
  @apply transition-all duration-200;
}

.status-connected {
  @apply bg-green-500/20 text-green-300 border border-green-500/30;
}

.status-connecting {
  @apply bg-yellow-500/20 text-yellow-300 border border-yellow-500/30;
}

.status-error {
  @apply bg-red-500/20 text-red-300 border border-red-500/30;
}

.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-connected .status-dot {
  @apply bg-green-400 animate-pulse;
}

.status-connecting .status-dot {
  @apply bg-yellow-400 animate-pulse;
}

.status-error .status-dot {
  @apply bg-red-400 animate-pulse;
}

.last-update {
  @apply text-sm text-slate-400;
}

/* 主要内容区域 */
.dashboard-content {
  @apply max-w-7xl mx-auto px-6 py-6 space-y-6;
}

/* 状态卡片组 */
.status-cards {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.status-card {
  @apply glass rounded-apple-lg p-6 border border-white/10;
  @apply hover:bg-white/5 transition-all duration-200;
  @apply transform hover:scale-105 hover:shadow-apple-lg;
}

.card-header {
  @apply flex items-center justify-between mb-4;
}

.card-title {
  @apply text-lg font-semibold text-white;
}

.health-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.health-healthy {
  @apply bg-green-500/20 text-green-300 border border-green-500/30;
}

.health-warning {
  @apply bg-yellow-500/20 text-yellow-300 border border-yellow-500/30;
}

.health-critical {
  @apply bg-red-500/20 text-red-300 border border-red-500/30;
}

.health-unknown {
  @apply bg-gray-500/20 text-gray-300 border border-gray-500/30;
}

.alert-badge {
  @apply bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full;
  @apply animate-pulse;
}

.card-content {
  @apply space-y-3;
}

.status-item {
  @apply flex justify-between items-center;
}

.status-label {
  @apply text-slate-400 text-sm;
}

.status-value {
  @apply text-white font-medium;
}

/* 实时数据区域 */
.realtime-section {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.realtime-card {
  @apply glass rounded-apple-lg p-6 border border-white/10;
}

/* 时间戳显示 */
.timestamp-display {
  @apply space-y-4;
}

.timestamp-main {
  @apply text-2xl font-mono font-bold text-center py-4;
  @apply bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent;
}

.timestamp-details {
  @apply grid grid-cols-3 gap-4;
}

.detail-item {
  @apply text-center;
}

.detail-label {
  @apply block text-xs text-slate-400 mb-1;
}

.detail-value {
  @apply block text-sm font-medium text-white;
}

/* 性能指标显示 */
.metrics-display {
  @apply space-y-4;
}

.metric-item {
  @apply space-y-2;
}

.metric-label {
  @apply text-sm text-slate-400;
}

.metric-value {
  @apply text-lg font-semibold text-white;
}

.metric-bar {
  @apply w-full h-2 bg-slate-700 rounded-full overflow-hidden;
}

.metric-fill {
  @apply h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full;
  @apply transition-all duration-500 ease-out;
}

/* 无数据状态 */
.no-data {
  @apply flex flex-col items-center justify-center py-8 text-slate-400;
}

.no-data-icon {
  @apply text-3xl mb-2;
}

.no-data-text {
  @apply text-sm;
}

/* 告警区域 */
.alerts-section {
  @apply glass rounded-apple-lg p-6 border border-white/10;
}

.section-header {
  @apply flex items-center justify-between mb-6;
}

.section-title {
  @apply text-xl font-semibold text-white;
}

.section-actions {
  @apply flex gap-2;
}

.alerts-list {
  @apply space-y-3 max-h-96 overflow-y-auto;
}

/* 告警项样式 */
.alert-item {
  @apply flex items-start gap-3 p-4 rounded-apple border border-white/10;
  @apply hover:bg-white/5 transition-all duration-200 cursor-pointer;
}

.alert-unread {
  @apply bg-white/5 border-blue-500/30;
}

.alert-critical {
  @apply border-red-500/30 bg-red-500/5;
}

.alert-error {
  @apply border-orange-500/30 bg-orange-500/5;
}

.alert-warning {
  @apply border-yellow-500/30 bg-yellow-500/5;
}

.alert-info {
  @apply border-blue-500/30 bg-blue-500/5;
}

.alert-icon {
  @apply text-lg flex-shrink-0 mt-0.5;
}

.alert-content {
  @apply flex-1 min-w-0;
}

.alert-header {
  @apply flex items-center justify-between mb-1;
}

.alert-component {
  @apply text-sm font-medium text-white;
}

.alert-time {
  @apply text-xs text-slate-400;
}

.alert-message {
  @apply text-sm text-slate-300 break-words;
}

.alert-actions {
  @apply flex gap-1 flex-shrink-0;
}

.alert-action-btn {
  @apply w-6 h-6 rounded-full bg-white/10 hover:bg-white/20;
  @apply flex items-center justify-center text-xs text-white;
  @apply transition-all duration-200;
}

.alert-action-btn:hover {
  @apply transform scale-110;
}

/* 无告警状态 */
.no-alerts {
  @apply flex flex-col items-center justify-center py-12 text-slate-400;
}

.no-alerts-icon {
  @apply text-4xl mb-3;
}

.no-alerts-text {
  @apply text-lg;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-content {
    @apply px-4 py-4;
  }

  .status-cards {
    @apply grid-cols-1 gap-4;
  }

  .realtime-section {
    @apply grid-cols-1;
  }

  .timestamp-details {
    @apply grid-cols-1 gap-2;
  }

  .header-content {
    @apply flex-col gap-3 items-start;
  }

  .connection-status {
    @apply flex-col gap-2 items-start;
  }
}

/* 滚动条样式 */
.alerts-list::-webkit-scrollbar {
  @apply w-2;
}

.alerts-list::-webkit-scrollbar-track {
  @apply bg-slate-800 rounded-full;
}

.alerts-list::-webkit-scrollbar-thumb {
  @apply bg-slate-600 rounded-full hover:bg-slate-500;
}
</style>
