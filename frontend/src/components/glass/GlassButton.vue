<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
    class="glass-button"
  >
    <div v-if="loading" class="glass-button-spinner">
      <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
    
    <div v-if="$slots.icon && !loading" class="glass-button-icon">
      <slot name="icon" />
    </div>
    
    <span v-if="$slots.default" class="glass-button-text">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  variant?: 'default' | 'primary' | 'secondary' | 'danger' | 'ghost'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  fullWidth?: boolean
  rounded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  disabled: false,
  loading: false,
  fullWidth: false,
  rounded: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}

const buttonClasses = computed(() => {
  const classes = ['glass-button']
  
  // Base styles
  classes.push('relative inline-flex items-center justify-center font-medium transition-all duration-200')
  classes.push('focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent')
  
  // Variant styles
  switch (props.variant) {
    case 'primary':
      classes.push('bg-blue-600 hover:bg-blue-700 text-white shadow-apple-lg')
      classes.push('focus:ring-blue-500/50')
      break
    case 'secondary':
      classes.push('glass border border-white/20 text-white hover:bg-white/10')
      classes.push('focus:ring-white/50')
      break
    case 'danger':
      classes.push('bg-red-600 hover:bg-red-700 text-white shadow-apple-lg')
      classes.push('focus:ring-red-500/50')
      break
    case 'ghost':
      classes.push('text-slate-300 hover:text-white hover:bg-white/5')
      classes.push('focus:ring-white/30')
      break
    default:
      classes.push('glass hover:bg-white/10 text-white')
      classes.push('focus:ring-white/50')
  }
  
  // Size styles
  switch (props.size) {
    case 'xs':
      classes.push('px-2 py-1 text-xs rounded-md')
      break
    case 'sm':
      classes.push('px-3 py-1.5 text-sm rounded-lg')
      break
    case 'lg':
      classes.push('px-6 py-3 text-lg rounded-apple-lg')
      break
    case 'xl':
      classes.push('px-8 py-4 text-xl rounded-apple-xl')
      break
    default:
      classes.push('px-4 py-2 text-sm rounded-apple')
  }
  
  // Full width
  if (props.fullWidth) {
    classes.push('w-full')
  }
  
  // Disabled state
  if (props.disabled || props.loading) {
    classes.push('opacity-50 cursor-not-allowed')
  } else {
    classes.push('active:scale-95')
  }
  
  // Rounded
  if (props.rounded) {
    classes.push('rounded-full')
  }
  
  return classes.join(' ')
})
</script>

<style scoped>
.glass-button {
  @apply relative overflow-hidden;
}

.glass-button::before {
  content: '';
  @apply absolute inset-0 rounded-inherit opacity-0 transition-opacity duration-200;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.glass-button:hover::before {
  @apply opacity-100;
}

.glass-button-spinner {
  @apply mr-2;
}

.glass-button-icon {
  @apply mr-2 flex-shrink-0;
}

.glass-button-text {
  @apply relative z-10;
}

/* Icon only button */
.glass-button:not(:has(.glass-button-text)) .glass-button-icon {
  @apply mr-0;
}
</style>
