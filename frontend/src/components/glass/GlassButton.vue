<!--
  苹果风格毛玻璃按钮组件

  功能特性：
  - 支持多种视觉变体（默认、主要、次要、危险、幽灵）
  - 支持多种尺寸（xs、sm、md、lg、xl）
  - 支持加载状态和禁用状态
  - 支持图标和文本内容
  - 支持全宽度和圆角样式
  - 内置毛玻璃效果和悬停动画
-->
<template>
  <!-- 主按钮容器 -->
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
    class="glass-button"
  >
    <!-- 加载状态旋转图标 -->
    <div v-if="loading" class="glass-button-spinner">
      <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>

    <!-- 图标插槽（非加载状态时显示） -->
    <div v-if="$slots.icon && !loading" class="glass-button-icon">
      <slot name="icon" />
    </div>

    <!-- 按钮文本内容 -->
    <span v-if="$slots.default" class="glass-button-text">
      <slot />
    </span>
  </button>
</template>

<!--
  苹果风格毛玻璃按钮组件的脚本逻辑

  主要功能：
  - 定义组件属性接口和默认值
  - 处理点击事件和状态管理
  - 动态计算按钮样式类名
-->
<script setup lang="ts">
import { computed } from 'vue'

/**
 * 按钮组件属性接口定义
 */
interface Props {
  /** 按钮视觉变体 */
  variant?: 'default' | 'primary' | 'secondary' | 'danger' | 'ghost'
  /** 按钮尺寸 */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示加载状态 */
  loading?: boolean
  /** 是否全宽度 */
  fullWidth?: boolean
  /** 是否圆角样式 */
  rounded?: boolean
}

/**
 * 组件属性定义，包含默认值
 */
const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  disabled: false,
  loading: false,
  fullWidth: false,
  rounded: false
})

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

/**
 * 处理按钮点击事件
 * 只有在非禁用且非加载状态下才触发点击事件
 */
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}

/**
 * 动态计算按钮的CSS类名
 * 根据不同的属性组合生成对应的样式类
 */
const buttonClasses = computed(() => {
  const classes = ['glass-button']

  // 基础样式：布局、字体、过渡效果、焦点样式
  classes.push('relative inline-flex items-center justify-center font-medium transition-all duration-200')
  classes.push('focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent')

  // 根据变体类型设置不同的视觉样式
  switch (props.variant) {
    case 'primary':
      // 主要按钮：蓝色背景，白色文字，阴影效果
      classes.push('bg-blue-600 hover:bg-blue-700 text-white shadow-apple-lg')
      classes.push('focus:ring-blue-500/50')
      break
    case 'secondary':
      // 次要按钮：毛玻璃效果，边框，悬停时背景变化
      classes.push('glass border border-white/20 text-white hover:bg-white/10')
      classes.push('focus:ring-white/50')
      break
    case 'danger':
      // 危险按钮：红色背景，用于删除等危险操作
      classes.push('bg-red-600 hover:bg-red-700 text-white shadow-apple-lg')
      classes.push('focus:ring-red-500/50')
      break
    case 'ghost':
      // 幽灵按钮：透明背景，仅在悬停时显示背景
      classes.push('text-slate-300 hover:text-white hover:bg-white/5')
      classes.push('focus:ring-white/30')
      break
    default:
      // 默认按钮：毛玻璃效果
      classes.push('glass hover:bg-white/10 text-white')
      classes.push('focus:ring-white/50')
  }

  // 根据尺寸设置内边距、字体大小和圆角
  switch (props.size) {
    case 'xs':
      classes.push('px-2 py-1 text-xs rounded-md')
      break
    case 'sm':
      classes.push('px-3 py-1.5 text-sm rounded-lg')
      break
    case 'lg':
      classes.push('px-6 py-3 text-lg rounded-apple-lg')
      break
    case 'xl':
      classes.push('px-8 py-4 text-xl rounded-apple-xl')
      break
    default:
      classes.push('px-4 py-2 text-sm rounded-apple')
  }

  // 全宽度样式
  if (props.fullWidth) {
    classes.push('w-full')
  }

  // 禁用和加载状态样式
  if (props.disabled || props.loading) {
    classes.push('opacity-50 cursor-not-allowed')
  } else {
    // 非禁用状态下添加点击缩放效果
    classes.push('active:scale-95')
  }

  // 圆角样式（覆盖默认圆角）
  if (props.rounded) {
    classes.push('rounded-full')
  }

  return classes.join(' ')
})
</script>

<!--
  苹果风格毛玻璃按钮的样式定义

  样式特性：
  - 毛玻璃背景效果和渐变叠加
  - 悬停时的光泽效果
  - 图标和文本的布局样式
  - 仅图标按钮的特殊处理
-->
<style scoped>
/* 按钮基础样式：相对定位，隐藏溢出内容 */
.glass-button {
  @apply relative overflow-hidden;
}

/* 按钮悬停光泽效果的伪元素 */
.glass-button::before {
  content: '';
  @apply absolute inset-0 rounded-inherit opacity-0 transition-opacity duration-200;
  /* 从左上到右下的白色渐变，营造光泽效果 */
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* 悬停时显示光泽效果 */
.glass-button:hover::before {
  @apply opacity-100;
}

/* 加载状态旋转图标的右边距 */
.glass-button-spinner {
  @apply mr-2;
}

/* 按钮图标样式：右边距，防止收缩 */
.glass-button-icon {
  @apply mr-2 flex-shrink-0;
}

/* 按钮文本样式：相对定位，确保在光泽效果之上 */
.glass-button-text {
  @apply relative z-10;
}

/* 仅图标按钮的特殊处理：移除图标的右边距 */
.glass-button:not(:has(.glass-button-text)) .glass-button-icon {
  @apply mr-0;
}
</style>
