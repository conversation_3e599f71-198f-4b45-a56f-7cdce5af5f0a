<template>
  <div :class="panelClasses" class="glass-panel">
    <div v-if="title || $slots.title" class="glass-panel-header">
      <slot name="title">
        <h3 class="text-lg font-semibold text-white">{{ title }}</h3>
      </slot>
      <div v-if="$slots.actions" class="glass-panel-actions">
        <slot name="actions" />
      </div>
    </div>
    
    <div class="glass-panel-content" :class="contentClasses">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title?: string
  variant?: 'default' | 'strong' | 'subtle'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  scrollable?: boolean
  maxHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  scrollable: false
})

const panelClasses = computed(() => {
  const classes = ['glass-panel']
  
  // Glass variant
  switch (props.variant) {
    case 'strong':
      classes.push('glass-strong')
      break
    case 'subtle':
      classes.push('glass-subtle')
      break
    default:
      classes.push('glass')
  }
  
  // Base styling
  classes.push('rounded-apple-lg overflow-hidden')
  
  return classes.join(' ')
})

const contentClasses = computed(() => {
  const classes = []
  
  // Padding
  switch (props.padding) {
    case 'none':
      break
    case 'sm':
      classes.push('p-3')
      break
    case 'lg':
      classes.push('p-6')
      break
    default:
      classes.push('p-4')
  }
  
  // Scrollable
  if (props.scrollable) {
    classes.push('overflow-y-auto scrollbar-apple')
    if (props.maxHeight) {
      classes.push(`max-h-[${props.maxHeight}]`)
    }
  }
  
  return classes.join(' ')
})
</script>

<style scoped>
.glass-panel {
  @apply relative;
}

.glass-panel-header {
  @apply flex items-center justify-between p-4 border-b border-white/10;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
}

.glass-panel-actions {
  @apply flex items-center space-x-2;
}

.glass-panel-content {
  @apply relative;
}
</style>
