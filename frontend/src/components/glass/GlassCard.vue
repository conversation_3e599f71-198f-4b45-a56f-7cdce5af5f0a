<template>
  <div 
    :class="cardClasses"
    class="glass-card"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div v-if="$slots.header" class="glass-card-header">
      <slot name="header" />
    </div>
    
    <div class="glass-card-content">
      <slot />
    </div>
    
    <div v-if="$slots.footer" class="glass-card-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface Props {
  variant?: 'default' | 'strong' | 'subtle'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  glow?: boolean
  glowColor?: 'blue' | 'green' | 'yellow' | 'red' | 'purple'
  rounded?: 'sm' | 'md' | 'lg' | 'xl'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  hover: true,
  glow: false,
  glowColor: 'blue',
  rounded: 'lg'
})

const isHovered = ref(false)

const handleMouseEnter = () => {
  if (props.hover) {
    isHovered.value = true
  }
}

const handleMouseLeave = () => {
  if (props.hover) {
    isHovered.value = false
  }
}

const cardClasses = computed(() => {
  const classes = []
  
  // Base glass effect
  switch (props.variant) {
    case 'strong':
      classes.push('glass-strong')
      break
    case 'subtle':
      classes.push('glass-subtle')
      break
    default:
      classes.push('glass')
  }
  
  // Size variants
  switch (props.size) {
    case 'sm':
      classes.push('p-3')
      break
    case 'lg':
      classes.push('p-6')
      break
    case 'xl':
      classes.push('p-8')
      break
    default:
      classes.push('p-4')
  }
  
  // Rounded corners
  switch (props.rounded) {
    case 'sm':
      classes.push('rounded-lg')
      break
    case 'md':
      classes.push('rounded-apple')
      break
    case 'lg':
      classes.push('rounded-apple-lg')
      break
    case 'xl':
      classes.push('rounded-apple-xl')
      break
    default:
      classes.push('rounded-apple-lg')
  }
  
  // Hover effects
  if (props.hover) {
    classes.push('transition-all duration-300 ease-out')
    if (isHovered.value) {
      classes.push('transform scale-[1.02] bg-white/10')
    }
  }
  
  // Glow effects
  if (props.glow) {
    classes.push(`glow-${props.glowColor}`)
  }
  
  return classes.join(' ')
})
</script>

<style scoped>
.glass-card {
  @apply relative overflow-hidden;
}

.glass-card::before {
  content: '';
  @apply absolute inset-0 rounded-inherit;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.glass-card-header {
  @apply border-b border-white/10 pb-3 mb-4;
}

.glass-card-content {
  @apply relative z-10;
}

.glass-card-footer {
  @apply border-t border-white/10 pt-3 mt-4;
}

/* Hover animation */
.glass-card:hover::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
}
</style>
