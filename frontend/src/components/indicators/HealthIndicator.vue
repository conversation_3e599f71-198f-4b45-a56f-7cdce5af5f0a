<template>
  <div class="health-indicator" :class="healthClass">
    <div class="health-icon">
      <svg v-if="health === 'healthy'" class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
      <svg v-else-if="health === 'warning'" class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <svg v-else class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div v-if="showLabel" class="health-label">
      <span class="health-text">{{ healthText }}</span>
      <span v-if="subtitle" class="health-subtitle">{{ subtitle }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  health: {
    type: String,
    default: 'healthy'
  },
  label: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  showLabel: {
    type: Boolean,
    default: true
  }
})

const healthClass = computed(() => {
  return [
    'health-indicator',
    `health-indicator--${props.health}`
  ]
})

const healthText = computed(() => {
  if (props.label) return props.label

  const healthMap = {
    healthy: '健康',
    warning: '警告',
    error: '错误'
  }
  return healthMap[props.health] || '未知'
})
</script>

<style scoped>
.health-indicator {
  @apply flex items-center space-x-2;
}

.health-icon {
  @apply flex-shrink-0;
}

.health-label {
  @apply flex flex-col;
}

.health-text {
  @apply text-sm font-medium;
}

.health-subtitle {
  @apply text-xs text-gray-400;
}

.health-indicator--healthy .health-text {
  @apply text-green-400;
}

.health-indicator--warning .health-text {
  @apply text-yellow-400;
}

.health-indicator--error .health-text {
  @apply text-red-400;
}
</style>
