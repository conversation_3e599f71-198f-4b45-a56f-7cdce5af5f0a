<!--
  主应用组件
  
  功能特性：
  - 全局布局管理
  - 错误边界处理
  - 主题和样式管理
  - 全局状态初始化
-->
<template>
  <div id="app" class="app-container">
    <!-- 全局加载指示器 -->
    <div v-if="isInitializing" class="app-loading">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <h2 class="loading-title">时间同步系统</h2>
        <p class="loading-text">正在初始化...</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="app-content">
      <!-- 错误边界 -->
      <div v-if="hasGlobalError" class="error-boundary">
        <div class="error-content">
          <div class="error-icon">⚠️</div>
          <h2 class="error-title">应用程序错误</h2>
          <p class="error-message">{{ globalError }}</p>
          <button class="error-retry-btn" @click="retryInitialization">
            重试
          </button>
        </div>
      </div>

      <!-- 正常内容 - 直接显示仪表板 -->
      <Dashboard v-else />
    </div>

    <!-- 全局通知容器 -->
    <div id="notifications" class="notifications-container"></div>
  </div>
</template>

<script setup lang="ts">
/**
 * 主应用组件脚本
 * 
 * 功能实现：
 * - 应用程序初始化
 * - 全局错误处理
 * - 状态管理初始化
 * - 主题和样式设置
 */

import { ref, onMounted, onErrorCaptured } from 'vue'
import { useSystemStore } from '@/stores/system'
import { useTimeSourcesStore } from '@/stores/timeSources'
import Dashboard from '@/components/Dashboard.vue'

// ==================== 状态定义 ====================

/** 是否正在初始化 */
const isInitializing = ref(true)

/** 是否有全局错误 */
const hasGlobalError = ref(false)

/** 全局错误信息 */
const globalError = ref<string | null>(null)

// ==================== Store 实例 ====================

const systemStore = useSystemStore()
const timeSourcesStore = useTimeSourcesStore()

// ==================== 方法定义 ====================

/**
 * 初始化应用程序
 */
async function initializeApp(): Promise<void> {
  try {
    console.log('🚀 开始初始化应用程序')
    
    // 设置全局错误处理
    setupGlobalErrorHandling()
    
    // 初始化基础数据（可选，不阻塞应用启动）
    try {
      await Promise.allSettled([
        systemStore.fetchVersionInfo(),
        timeSourcesStore.fetchTimeSources({ page: 1, page_size: 10 })
      ])
    } catch (error) {
      console.warn('⚠️ 初始数据加载失败，但不影响应用启动:', error)
    }
    
    console.log('✅ 应用程序初始化完成')
    
  } catch (error) {
    console.error('❌ 应用程序初始化失败:', error)
    hasGlobalError.value = true
    globalError.value = error instanceof Error ? error.message : '未知错误'
  } finally {
    isInitializing.value = false
  }
}

/**
 * 设置全局错误处理
 */
function setupGlobalErrorHandling(): void {
  // 处理未捕获的 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('❌ 未处理的 Promise 错误:', event.reason)
    
    // 可以选择阻止默认的错误处理
    // event.preventDefault()
  })
  
  // 处理全局 JavaScript 错误
  window.addEventListener('error', (event) => {
    console.error('❌ 全局 JavaScript 错误:', event.error)
  })
}

/**
 * 重试初始化
 */
async function retryInitialization(): Promise<void> {
  hasGlobalError.value = false
  globalError.value = null
  isInitializing.value = true
  
  await initializeApp()
}

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载时初始化应用
 */
onMounted(async () => {
  await initializeApp()
})

/**
 * 捕获子组件错误
 */
onErrorCaptured((error, instance, info) => {
  console.error('❌ 组件错误:', error, info)
  
  // 可以选择是否阻止错误向上传播
  // return false
})
</script>

<!--
  全局样式定义
  
  设计特色：
  - 现代化的深色主题
  - 苹果风格的毛玻璃效果
  - 平滑的动画过渡
  - 响应式设计
-->
<style>
/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #0f172a;
  color: #ffffff;
  overflow-x: hidden;
}

/* 应用容器 */
.app-container {
  min-height: 100vh;
  position: relative;
}

/* 加载状态样式 */
.app-loading {
  position: fixed;
  inset: 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
}

.spinner-ring {
  position: absolute;
  inset: 0;
  border: 4px solid transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #3b82f6;
  animation-duration: 1.5s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #8b5cf6;
  animation-duration: 2s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #ec4899;
  animation-duration: 2.5s;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 8px;
}

.loading-text {
  color: #94a3b8;
}

/* 应用内容区域 */
.app-content {
  min-height: 100vh;
}

/* 错误边界样式 */
.error-boundary {
  position: fixed;
  inset: 0;
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 50%, #7f1d1d 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.error-content {
  text-align: center;
  max-width: 28rem;
  margin: 0 auto;
  padding: 2rem;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.error-message {
  color: #fecaca;
  margin-bottom: 1.5rem;
}

.error-retry-btn {
  padding: 0.75rem 1.5rem;
  background-color: #dc2626;
  color: white;
  font-weight: 500;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-retry-btn:hover {
  background-color: #b91c1c;
}

.error-retry-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px #dc2626, 0 0 0 4px rgba(220, 38, 38, 0.2);
}

/* 通知容器 */
.notifications-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 50;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  pointer-events: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* 选择文本样式 */
::selection {
  background: rgba(59, 130, 246, 0.3);
}

/* 焦点样式 */
:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .loading-content {
    padding: 0 1rem;
  }
  
  .error-content {
    padding: 1rem;
  }
  
  .notifications-container {
    left: 1rem;
    right: 1rem;
  }
}
</style>
