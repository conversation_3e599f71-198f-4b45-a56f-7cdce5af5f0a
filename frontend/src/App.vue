<template>
  <div id="app" class="h-screen bg-gray-900 text-white flex overflow-hidden">
    <!-- 左侧导航栏 -->
    <aside class="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
      <!-- Logo区域 -->
      <div class="p-4 border-b border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
            </svg>
          </div>
          <div>
            <h1 class="text-sm font-semibold">原子钟授时服务器</h1>
            <p class="text-xs text-gray-400">高精度时间同步系统</p>
          </div>
        </div>
      </div>

      <!-- 导航菜单 -->
      <nav class="flex-1 p-4">
        <ul class="space-y-2">
          <li v-for="item in navigationMenuItems" :key="item.key">
            <router-link 
              :to="item.to"
              class="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors"
              :class="{ 
                'bg-blue-600 text-white': $route.name === item.to.name,
                'text-gray-300 hover:bg-gray-700 hover:text-white': $route.name !== item.to.name
              }"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path v-if="item.key === 'dashboard'" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                <path v-else-if="item.key === 'status'" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                <path v-else-if="item.key === 'timesources'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                <path v-else-if="item.key === 'services'" d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                <path v-else d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              </svg>
              <span>{{ item.label }}</span>
            </router-link>
          </li>
        </ul>
      </nav>

      <!-- 底部信息 -->
      <div class="p-4 border-t border-gray-700">
        <div class="text-xs text-gray-400">
          <div>版本 {{ version }}</div>
          <div>© {{ currentYear }}</div>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="flex-1 flex flex-col overflow-hidden">
      <!-- 顶部状态栏 -->
      <header class="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-semibold">{{ currentPageTitle }}</h2>
            <p class="text-sm text-gray-400">{{ currentPageDescription }}</p>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- 系统状态 -->
            <div class="flex items-center space-x-2">
              <div 
                class="w-2 h-2 rounded-full"
                :class="{
                  'bg-green-400': systemStatus.health === 'healthy',
                  'bg-yellow-400': systemStatus.health === 'warning', 
                  'bg-red-400': systemStatus.health === 'error'
                }"
              ></div>
              <span class="text-sm">{{ systemStatusText }}</span>
            </div>

            <!-- 当前时间 -->
            <div class="text-sm font-mono">
              {{ currentTime }}
            </div>

            <!-- 用户菜单 -->
            <div class="flex items-center space-x-2">
              <button 
                @click="toggleTheme"
                class="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
              >
                <svg v-if="isDark" class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
                </svg>
                <svg v-else class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M17.293 13.293A8 8 0 716.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="flex-1 overflow-auto bg-gray-900">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const isDark = ref(true)
const currentTime = ref('')
const systemStatus = ref({
  health: 'healthy',
  uptime: 0
})

// 更新时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN')
}

// 定时器
let timeInterval = null

// 导航菜单项
const navigationMenuItems = computed(() => [
  { key: 'dashboard', label: '系统概览', to: { name: 'dashboard' } },
  { key: 'status', label: '系统状态', to: { name: 'status' } },
  { key: 'timesources', label: '时间源管理', to: { name: 'timesources' } },
  { key: 'services', label: '网络服务', to: { name: 'services' } },
  { key: 'config', label: '系统配置', to: { name: 'config' } }
])

const toggleTheme = () => {
  isDark.value = !isDark.value
}

const systemStatusText = computed(() => {
  const statusMap = { healthy: '正常', warning: '警告', error: '错误' }
  return statusMap[systemStatus.value.health] || '未知'
})

const currentYear = computed(() => new Date().getFullYear())
const version = computed(() => '0.1.0')

const currentPageTitle = computed(() => {
  const routeMap = {
    dashboard: '系统概览',
    status: '系统状态',
    timesources: '时间源管理',
    services: '网络服务',
    config: '系统配置'
  }
  return routeMap[route.name] || '原子钟授时服务器'
})

const currentPageDescription = computed(() => {
  const descMap = {
    dashboard: '实时监控和数据可视化',
    status: '查看系统运行状态和健康信息',
    timesources: '管理和配置时间源',
    services: '管理 NTP 和 PTP 服务',
    config: '系统配置和参数设置'
  }
  return descMap[route.name] || '高精度时间同步系统'
})

onMounted(async () => {
  // 启动时间更新
  updateTime()
  timeInterval = setInterval(updateTime, 1000)

  // 获取系统状态
  try {
    const response = await fetch('/api/status')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        systemStatus.value.health = data.data.health || 'healthy'
      }
    }
  } catch (error) {
    console.error('Failed to fetch status:', error)
    systemStatus.value.health = 'error'
  }
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
