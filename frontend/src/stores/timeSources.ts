/**
 * 时间源管理 Store
 * 
 * 使用 Pinia 管理时间源相关的状态，包括：
 * - 时间源列表
 * - 时间源详情
 * - 时间源操作（创建、更新、删除）
 * - 实时状态更新
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/api/client'
import type { 
  TimeSource, 
  TimeSourceList, 
  TimeSourceStatus,
  TimeSourceType,
  HealthStatus,
  PaginationInfo 
} from '@/api/types'

/**
 * 时间源过滤条件
 */
interface TimeSourceFilters {
  /** 时间源类型 */
  type?: TimeSourceType
  /** 健康状态 */
  health?: HealthStatus
  /** 是否活跃 */
  isActive?: boolean
  /** 是否可用 */
  isAvailable?: boolean
  /** 搜索关键词 */
  search?: string
}

/**
 * 时间源排序选项
 */
interface TimeSourceSorting {
  /** 排序字段 */
  field: 'name' | 'type' | 'quality_score' | 'last_update' | 'created_at'
  /** 排序方向 */
  direction: 'asc' | 'desc'
}

/**
 * 时间源管理 Store
 * 
 * 使用 Setup Store 语法，提供响应式的时间源状态管理
 */
export const useTimeSourcesStore = defineStore('timeSources', () => {
  // ==================== 状态定义 ====================
  
  /** 时间源列表 */
  const timeSources = ref<TimeSource[]>([])
  
  /** 当前选中的时间源 */
  const selectedTimeSource = ref<TimeSource | null>(null)
  
  /** 分页信息 */
  const pagination = ref<PaginationInfo>({
    page: 1,
    page_size: 20,
    total_pages: 0,
    total_records: 0
  })
  
  /** 过滤条件 */
  const filters = ref<TimeSourceFilters>({})
  
  /** 排序选项 */
  const sorting = ref<TimeSourceSorting>({
    field: 'name',
    direction: 'asc'
  })
  
  /** 加载状态 */
  const loading = ref({
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false
  })
  
  /** 错误信息 */
  const errors = ref({
    list: null as string | null,
    detail: null as string | null,
    create: null as string | null,
    update: null as string | null,
    delete: null as string | null
  })
  
  /** 最后更新时间 */
  const lastUpdated = ref<Date | null>(null)

  // ==================== 计算属性 (Getters) ====================
  
  /** 过滤后的时间源列表 */
  const filteredTimeSources = computed(() => {
    let result = [...timeSources.value]
    
    // 按类型过滤
    if (filters.value.type) {
      result = result.filter(source => source.source_type === filters.value.type)
    }
    
    // 按健康状态过滤
    if (filters.value.health) {
      result = result.filter(source => source.status.health === filters.value.health)
    }
    
    // 按活跃状态过滤
    if (filters.value.isActive !== undefined) {
      result = result.filter(source => source.status.is_active === filters.value.isActive)
    }
    
    // 按可用状态过滤
    if (filters.value.isAvailable !== undefined) {
      result = result.filter(source => source.status.is_available === filters.value.isAvailable)
    }
    
    // 按搜索关键词过滤
    if (filters.value.search) {
      const searchLower = filters.value.search.toLowerCase()
      result = result.filter(source => 
        source.name.toLowerCase().includes(searchLower) ||
        source.id.toLowerCase().includes(searchLower) ||
        source.status.status_message.toLowerCase().includes(searchLower)
      )
    }
    
    // 排序
    result.sort((a, b) => {
      const { field, direction } = sorting.value
      let aValue: any, bValue: any
      
      switch (field) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'type':
          aValue = a.source_type
          bValue = b.source_type
          break
        case 'quality_score':
          aValue = a.status.quality_score
          bValue = b.status.quality_score
          break
        case 'last_update':
          aValue = new Date(a.status.last_update)
          bValue = new Date(b.status.last_update)
          break
        case 'created_at':
          aValue = new Date(a.created_at)
          bValue = new Date(b.created_at)
          break
        default:
          return 0
      }
      
      if (aValue < bValue) return direction === 'asc' ? -1 : 1
      if (aValue > bValue) return direction === 'asc' ? 1 : -1
      return 0
    })
    
    return result
  })
  
  /** 活跃的时间源数量 */
  const activeTimeSourcesCount = computed(() => {
    return timeSources.value.filter(source => source.status.is_active).length
  })
  
  /** 可用的时间源数量 */
  const availableTimeSourcesCount = computed(() => {
    return timeSources.value.filter(source => source.status.is_available).length
  })
  
  /** 健康的时间源数量 */
  const healthyTimeSourcesCount = computed(() => {
    return timeSources.value.filter(source => source.status.health === 'Healthy').length
  })
  
  /** 警告状态的时间源数量 */
  const warningTimeSourcesCount = computed(() => {
    return timeSources.value.filter(source => source.status.health === 'Warning').length
  })
  
  /** 危险状态的时间源数量 */
  const criticalTimeSourcesCount = computed(() => {
    return timeSources.value.filter(source => source.status.health === 'Critical').length
  })
  
  /** 按类型分组的时间源 */
  const timeSourcesByType = computed(() => {
    const groups: Record<TimeSourceType, TimeSource[]> = {
      Pps: [],
      TenMhz: [],
      Tod: [],
      Rtc: [],
      System: [],
      Network: []
    }
    
    timeSources.value.forEach(source => {
      groups[source.source_type].push(source)
    })
    
    return groups
  })
  
  /** 平均质量评分 */
  const averageQualityScore = computed(() => {
    if (timeSources.value.length === 0) return 0
    
    const totalScore = timeSources.value.reduce((sum, source) => sum + source.status.quality_score, 0)
    return totalScore / timeSources.value.length
  })
  
  /** 是否有任何加载中的操作 */
  const isLoading = computed(() => {
    return Object.values(loading.value).some(Boolean)
  })
  
  /** 是否有任何错误 */
  const hasErrors = computed(() => {
    return Object.values(errors.value).some(Boolean)
  })

  // ==================== 操作方法 (Actions) ====================
  
  /**
   * 获取时间源列表
   */
  async function fetchTimeSources(params?: { page?: number; page_size?: number }): Promise<void> {
    loading.value.list = true
    errors.value.list = null
    
    try {
      const response = await api.getTimeSources(params)
      
      if (response.success && response.data) {
        timeSources.value = response.data.sources
        
        // 更新分页信息
        if (response.data.pagination) {
          pagination.value = response.data.pagination
        }
        
        lastUpdated.value = new Date()
        console.log('✅ 时间源列表获取成功')
      } else {
        throw new Error(response.error || '获取时间源列表失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.list = errorMessage
      console.error('❌ 获取时间源列表失败:', errorMessage)
    } finally {
      loading.value.list = false
    }
  }
  
  /**
   * 获取时间源详情
   */
  async function fetchTimeSource(id: string): Promise<void> {
    loading.value.detail = true
    errors.value.detail = null
    
    try {
      const response = await api.getTimeSource(id)
      
      if (response.success && response.data) {
        selectedTimeSource.value = response.data
        
        // 同时更新列表中的对应项
        const index = timeSources.value.findIndex(source => source.id === id)
        if (index !== -1) {
          timeSources.value[index] = response.data
        }
        
        console.log(`✅ 时间源详情获取成功: ${id}`)
      } else {
        throw new Error(response.error || '获取时间源详情失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.detail = errorMessage
      console.error(`❌ 获取时间源详情失败 (${id}):`, errorMessage)
    } finally {
      loading.value.detail = false
    }
  }
  
  /**
   * 创建时间源
   */
  async function createTimeSource(data: Partial<TimeSource>): Promise<boolean> {
    loading.value.create = true
    errors.value.create = null
    
    try {
      const response = await api.createTimeSource(data)
      
      if (response.success && response.data) {
        // 添加到列表中
        timeSources.value.push(response.data)
        console.log(`✅ 时间源创建成功: ${response.data.name}`)
        return true
      } else {
        throw new Error(response.error || '创建时间源失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.create = errorMessage
      console.error('❌ 创建时间源失败:', errorMessage)
      return false
    } finally {
      loading.value.create = false
    }
  }
  
  /**
   * 更新时间源
   */
  async function updateTimeSource(id: string, data: Partial<TimeSource>): Promise<boolean> {
    loading.value.update = true
    errors.value.update = null
    
    try {
      const response = await api.updateTimeSource(id, data)
      
      if (response.success && response.data) {
        // 更新列表中的对应项
        const index = timeSources.value.findIndex(source => source.id === id)
        if (index !== -1) {
          timeSources.value[index] = response.data
        }
        
        // 更新选中的时间源
        if (selectedTimeSource.value?.id === id) {
          selectedTimeSource.value = response.data
        }
        
        console.log(`✅ 时间源更新成功: ${id}`)
        return true
      } else {
        throw new Error(response.error || '更新时间源失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.update = errorMessage
      console.error(`❌ 更新时间源失败 (${id}):`, errorMessage)
      return false
    } finally {
      loading.value.update = false
    }
  }
  
  /**
   * 删除时间源
   */
  async function deleteTimeSource(id: string): Promise<boolean> {
    loading.value.delete = true
    errors.value.delete = null
    
    try {
      const response = await api.deleteTimeSource(id)
      
      if (response.success) {
        // 从列表中移除
        const index = timeSources.value.findIndex(source => source.id === id)
        if (index !== -1) {
          timeSources.value.splice(index, 1)
        }
        
        // 清除选中状态
        if (selectedTimeSource.value?.id === id) {
          selectedTimeSource.value = null
        }
        
        console.log(`✅ 时间源删除成功: ${id}`)
        return true
      } else {
        throw new Error(response.error || '删除时间源失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.delete = errorMessage
      console.error(`❌ 删除时间源失败 (${id}):`, errorMessage)
      return false
    } finally {
      loading.value.delete = false
    }
  }
  
  /**
   * 设置过滤条件
   */
  function setFilters(newFilters: Partial<TimeSourceFilters>): void {
    filters.value = { ...filters.value, ...newFilters }
  }
  
  /**
   * 清除过滤条件
   */
  function clearFilters(): void {
    filters.value = {}
  }
  
  /**
   * 设置排序选项
   */
  function setSorting(newSorting: Partial<TimeSourceSorting>): void {
    sorting.value = { ...sorting.value, ...newSorting }
  }
  
  /**
   * 选择时间源
   */
  function selectTimeSource(timeSource: TimeSource | null): void {
    selectedTimeSource.value = timeSource
  }
  
  /**
   * 根据ID查找时间源
   */
  function findTimeSourceById(id: string): TimeSource | undefined {
    return timeSources.value.find(source => source.id === id)
  }
  
  /**
   * 更新时间源状态（用于实时更新）
   */
  function updateTimeSourceStatus(id: string, status: Partial<TimeSourceStatus>): void {
    const timeSource = findTimeSourceById(id)
    if (timeSource) {
      timeSource.status = { ...timeSource.status, ...status }
      timeSource.updated_at = new Date().toISOString()
      
      // 同时更新选中的时间源
      if (selectedTimeSource.value?.id === id) {
        selectedTimeSource.value = timeSource
      }
    }
  }
  
  /**
   * 刷新时间源列表
   */
  async function refreshTimeSources(): Promise<void> {
    await fetchTimeSources({
      page: pagination.value.page,
      page_size: pagination.value.page_size
    })
  }
  
  /**
   * 清除错误信息
   */
  function clearErrors(): void {
    errors.value = {
      list: null,
      detail: null,
      create: null,
      update: null,
      delete: null
    }
  }
  
  /**
   * 清除特定类型的错误
   */
  function clearError(type: keyof typeof errors.value): void {
    errors.value[type] = null
  }

  // ==================== 返回 Store 接口 ====================
  
  return {
    // 状态
    timeSources,
    selectedTimeSource,
    pagination,
    filters,
    sorting,
    loading,
    errors,
    lastUpdated,
    
    // 计算属性
    filteredTimeSources,
    activeTimeSourcesCount,
    availableTimeSourcesCount,
    healthyTimeSourcesCount,
    warningTimeSourcesCount,
    criticalTimeSourcesCount,
    timeSourcesByType,
    averageQualityScore,
    isLoading,
    hasErrors,
    
    // 操作方法
    fetchTimeSources,
    fetchTimeSource,
    createTimeSource,
    updateTimeSource,
    deleteTimeSource,
    setFilters,
    clearFilters,
    setSorting,
    selectTimeSource,
    findTimeSourceById,
    updateTimeSourceStatus,
    refreshTimeSources,
    clearErrors,
    clearError
  }
})
