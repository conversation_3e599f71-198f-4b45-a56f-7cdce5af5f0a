/**
 * 系统状态管理 Store
 * 
 * 使用 Pinia 管理系统相关的状态，包括：
 * - 系统状态信息
 * - 版本信息
 * - 服务状态
 * - 系统操作（重启、关闭等）
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/api/client'
import type { 
  SystemStatus, 
  VersionInfo, 
  ServiceList, 
  ServiceInfo,
  OperationResult,
  HealthStatus 
} from '@/api/types'

/**
 * 系统 Store 状态接口
 */
interface SystemState {
  /** 系统状态信息 */
  status: SystemStatus | null
  /** 版本信息 */
  version: VersionInfo | null
  /** 服务列表 */
  services: ServiceInfo[]
  /** 加载状态 */
  loading: {
    status: boolean
    version: boolean
    services: boolean
    operations: boolean
  }
  /** 错误信息 */
  errors: {
    status: string | null
    version: string | null
    services: string | null
    operations: string | null
  }
  /** 最后更新时间 */
  lastUpdated: {
    status: Date | null
    version: Date | null
    services: Date | null
  }
}

/**
 * 系统状态管理 Store
 * 
 * 使用 Setup Store 语法，提供响应式的系统状态管理
 */
export const useSystemStore = defineStore('system', () => {
  // ==================== 状态定义 ====================
  
  /** 系统状态信息 */
  const status = ref<SystemStatus | null>(null)
  
  /** 版本信息 */
  const version = ref<VersionInfo | null>(null)
  
  /** 服务列表 */
  const services = ref<ServiceInfo[]>([])
  
  /** 加载状态 */
  const loading = ref({
    status: false,
    version: false,
    services: false,
    operations: false
  })
  
  /** 错误信息 */
  const errors = ref({
    status: null as string | null,
    version: null as string | null,
    services: null as string | null,
    operations: null as string | null
  })
  
  /** 最后更新时间 */
  const lastUpdated = ref({
    status: null as Date | null,
    version: null as Date | null,
    services: null as Date | null
  })

  // ==================== 计算属性 (Getters) ====================
  
  /** 系统是否健康 */
  const isSystemHealthy = computed(() => {
    if (!status.value) return false
    return status.value.status === 'healthy'
  })
  
  /** 系统运行时间（格式化） */
  const formattedUptime = computed(() => {
    if (!version.value?.runtime.uptime_seconds) return '未知'
    
    const seconds = version.value.runtime.uptime_seconds
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  })
  
  /** 运行中的服务数量 */
  const runningServicesCount = computed(() => {
    return services.value.filter(service => service.status === 'Running').length
  })
  
  /** 停止的服务数量 */
  const stoppedServicesCount = computed(() => {
    return services.value.filter(service => service.status === 'Stopped').length
  })
  
  /** 失败的服务数量 */
  const failedServicesCount = computed(() => {
    return services.value.filter(service => service.status === 'Failed').length
  })
  
  /** 服务总数 */
  const totalServicesCount = computed(() => {
    return services.value.length
  })
  
  /** 系统整体健康状态 */
  const overallHealth = computed((): HealthStatus => {
    if (!status.value) return 'Unknown'
    
    // 如果有失败的服务，系统状态为危险
    if (failedServicesCount.value > 0) return 'Critical'
    
    // 如果有停止的服务，系统状态为警告
    if (stoppedServicesCount.value > 0) return 'Warning'
    
    // 如果系统状态不健康，返回警告
    if (!isSystemHealthy.value) return 'Warning'
    
    return 'Healthy'
  })
  
  /** 是否有任何加载中的操作 */
  const isLoading = computed(() => {
    return Object.values(loading.value).some(Boolean)
  })
  
  /** 是否有任何错误 */
  const hasErrors = computed(() => {
    return Object.values(errors.value).some(Boolean)
  })

  // ==================== 操作方法 (Actions) ====================
  
  /**
   * 获取系统状态
   */
  async function fetchSystemStatus(): Promise<void> {
    loading.value.status = true
    errors.value.status = null
    
    try {
      const response = await api.getSystemStatus()
      
      if (response.success && response.data) {
        status.value = response.data
        lastUpdated.value.status = new Date()
        console.log('✅ 系统状态获取成功')
      } else {
        throw new Error(response.error || '获取系统状态失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.status = errorMessage
      console.error('❌ 获取系统状态失败:', errorMessage)
    } finally {
      loading.value.status = false
    }
  }
  
  /**
   * 获取版本信息
   */
  async function fetchVersionInfo(): Promise<void> {
    loading.value.version = true
    errors.value.version = null
    
    try {
      const response = await api.getVersionInfo()
      
      if (response.success && response.data) {
        version.value = response.data
        lastUpdated.value.version = new Date()
        console.log('✅ 版本信息获取成功')
      } else {
        throw new Error(response.error || '获取版本信息失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.version = errorMessage
      console.error('❌ 获取版本信息失败:', errorMessage)
    } finally {
      loading.value.version = false
    }
  }
  
  /**
   * 获取服务列表
   */
  async function fetchServices(): Promise<void> {
    loading.value.services = true
    errors.value.services = null
    
    try {
      const response = await api.getServices()
      
      if (response.success && response.data) {
        services.value = response.data.services
        lastUpdated.value.services = new Date()
        console.log('✅ 服务列表获取成功')
      } else {
        throw new Error(response.error || '获取服务列表失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.services = errorMessage
      console.error('❌ 获取服务列表失败:', errorMessage)
    } finally {
      loading.value.services = false
    }
  }
  
  /**
   * 启动服务
   */
  async function startService(serviceName: string): Promise<boolean> {
    loading.value.operations = true
    errors.value.operations = null
    
    try {
      const response = await api.startService(serviceName)
      
      if (response.success) {
        console.log(`✅ 服务 ${serviceName} 启动成功`)
        // 重新获取服务列表以更新状态
        await fetchServices()
        return true
      } else {
        throw new Error(response.error || `启动服务 ${serviceName} 失败`)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.operations = errorMessage
      console.error(`❌ 启动服务 ${serviceName} 失败:`, errorMessage)
      return false
    } finally {
      loading.value.operations = false
    }
  }
  
  /**
   * 停止服务
   */
  async function stopService(serviceName: string): Promise<boolean> {
    loading.value.operations = true
    errors.value.operations = null
    
    try {
      const response = await api.stopService(serviceName)
      
      if (response.success) {
        console.log(`✅ 服务 ${serviceName} 停止成功`)
        // 重新获取服务列表以更新状态
        await fetchServices()
        return true
      } else {
        throw new Error(response.error || `停止服务 ${serviceName} 失败`)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.operations = errorMessage
      console.error(`❌ 停止服务 ${serviceName} 失败:`, errorMessage)
      return false
    } finally {
      loading.value.operations = false
    }
  }
  
  /**
   * 重启服务
   */
  async function restartService(serviceName: string): Promise<boolean> {
    loading.value.operations = true
    errors.value.operations = null
    
    try {
      const response = await api.restartService(serviceName)
      
      if (response.success) {
        console.log(`✅ 服务 ${serviceName} 重启成功`)
        // 重新获取服务列表以更新状态
        await fetchServices()
        return true
      } else {
        throw new Error(response.error || `重启服务 ${serviceName} 失败`)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.operations = errorMessage
      console.error(`❌ 重启服务 ${serviceName} 失败:`, errorMessage)
      return false
    } finally {
      loading.value.operations = false
    }
  }
  
  /**
   * 重启系统
   */
  async function restartSystem(): Promise<boolean> {
    loading.value.operations = true
    errors.value.operations = null
    
    try {
      const response = await api.restartSystem()
      
      if (response.success) {
        console.log('✅ 系统重启命令发送成功')
        return true
      } else {
        throw new Error(response.error || '系统重启失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.operations = errorMessage
      console.error('❌ 系统重启失败:', errorMessage)
      return false
    } finally {
      loading.value.operations = false
    }
  }
  
  /**
   * 关闭系统
   */
  async function shutdownSystem(): Promise<boolean> {
    loading.value.operations = true
    errors.value.operations = null
    
    try {
      const response = await api.shutdownSystem()
      
      if (response.success) {
        console.log('✅ 系统关闭命令发送成功')
        return true
      } else {
        throw new Error(response.error || '系统关闭失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      errors.value.operations = errorMessage
      console.error('❌ 系统关闭失败:', errorMessage)
      return false
    } finally {
      loading.value.operations = false
    }
  }
  
  /**
   * 刷新所有数据
   */
  async function refreshAll(): Promise<void> {
    await Promise.all([
      fetchSystemStatus(),
      fetchVersionInfo(),
      fetchServices()
    ])
  }
  
  /**
   * 清除错误信息
   */
  function clearErrors(): void {
    errors.value = {
      status: null,
      version: null,
      services: null,
      operations: null
    }
  }
  
  /**
   * 清除特定类型的错误
   */
  function clearError(type: keyof typeof errors.value): void {
    errors.value[type] = null
  }

  // ==================== 返回 Store 接口 ====================
  
  return {
    // 状态
    status,
    version,
    services,
    loading,
    errors,
    lastUpdated,
    
    // 计算属性
    isSystemHealthy,
    formattedUptime,
    runningServicesCount,
    stoppedServicesCount,
    failedServicesCount,
    totalServicesCount,
    overallHealth,
    isLoading,
    hasErrors,
    
    // 操作方法
    fetchSystemStatus,
    fetchVersionInfo,
    fetchServices,
    startService,
    stopService,
    restartService,
    restartSystem,
    shutdownSystem,
    refreshAll,
    clearErrors,
    clearError
  }
})
