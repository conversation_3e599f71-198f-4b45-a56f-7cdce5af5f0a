/**
 * 实时数据管理 Store
 * 
 * 使用 Pinia 管理实时数据相关的状态，包括：
 * - WebSocket 连接管理
 * - 实时数据接收和处理
 * - 告警消息管理
 * - 性能指标监控
 * - 数据历史记录
 */

import { defineStore } from 'pinia'
import { ref, computed, onUnmounted } from 'vue'
import { useWebSocket, WebSocketState } from '@/api/websocket'
import type { 
  RealtimeDataMessage, 
  AlertMessage, 
  WebSocketMessage 
} from '@/api/types'

/**
 * 实时时间戳数据
 */
interface TimestampData {
  /** 时间戳（秒） */
  timestamp: number
  /** 纳秒部分 */
  nanoseconds: number
  /** 精度 */
  precision: number
  /** 时间源 */
  source: string
  /** 接收时间 */
  receivedAt: Date
}

/**
 * 性能指标数据
 */
interface MetricsData {
  /** CPU 使用率 */
  cpu_usage?: number
  /** 内存使用率 */
  memory_usage?: number
  /** 网络流量 */
  network_traffic?: {
    rx_bytes: number
    tx_bytes: number
  }
  /** 磁盘使用率 */
  disk_usage?: number
  /** 系统负载 */
  system_load?: number[]
  /** 接收时间 */
  receivedAt: Date
}

/**
 * 告警数据
 */
interface AlertData extends AlertMessage {
  /** 接收时间 */
  receivedAt: Date
  /** 是否已读 */
  isRead: boolean
  /** 是否已确认 */
  isAcknowledged: boolean
}

/**
 * 数据历史记录配置
 */
interface HistoryConfig {
  /** 最大记录数 */
  maxRecords: number
  /** 数据保留时间（毫秒） */
  retentionTime: number
}

/**
 * 实时数据管理 Store
 * 
 * 使用 Setup Store 语法，提供响应式的实时数据管理
 */
export const useRealtimeStore = defineStore('realtime', () => {
  // ==================== WebSocket 连接管理 ====================
  
  const { 
    client: wsClient, 
    state: wsState, 
    stats: wsStats,
    connect,
    disconnect,
    subscribeToRealtime,
    subscribeToAlerts,
    subscribeToMetrics,
    onError,
    onStateChange
  } = useWebSocket()

  // ==================== 状态定义 ====================
  
  /** 最新的时间戳数据 */
  const latestTimestamp = ref<TimestampData | null>(null)
  
  /** 时间戳历史数据 */
  const timestampHistory = ref<TimestampData[]>([])
  
  /** 最新的性能指标 */
  const latestMetrics = ref<MetricsData | null>(null)
  
  /** 性能指标历史数据 */
  const metricsHistory = ref<MetricsData[]>([])
  
  /** 告警列表 */
  const alerts = ref<AlertData[]>([])
  
  /** 连接错误信息 */
  const connectionError = ref<string | null>(null)
  
  /** 数据接收统计 */
  const dataStats = ref({
    timestampCount: 0,
    metricsCount: 0,
    alertCount: 0,
    lastDataReceived: null as Date | null
  })
  
  /** 历史记录配置 */
  const historyConfig = ref<HistoryConfig>({
    maxRecords: 1000,
    retentionTime: 24 * 60 * 60 * 1000 // 24小时
  })
  
  /** 订阅状态 */
  const subscriptions = ref({
    realtime: false,
    alerts: false,
    metrics: false
  })

  // ==================== 计算属性 (Getters) ====================
  
  /** WebSocket 是否已连接 */
  const isConnected = computed(() => wsState.value === WebSocketState.CONNECTED)
  
  /** 是否正在连接 */
  const isConnecting = computed(() => wsState.value === WebSocketState.CONNECTING)
  
  /** 连接是否有错误 */
  const hasConnectionError = computed(() => wsState.value === WebSocketState.ERROR)
  
  /** 未读告警数量 */
  const unreadAlertsCount = computed(() => {
    return alerts.value.filter(alert => !alert.isRead).length
  })
  
  /** 未确认告警数量 */
  const unacknowledgedAlertsCount = computed(() => {
    return alerts.value.filter(alert => !alert.isAcknowledged).length
  })
  
  /** 按级别分组的告警 */
  const alertsByLevel = computed(() => {
    const groups: Record<string, AlertData[]> = {
      critical: [],
      error: [],
      warning: [],
      info: []
    }
    
    alerts.value.forEach(alert => {
      if (groups[alert.level]) {
        groups[alert.level].push(alert)
      }
    })
    
    return groups
  })
  
  /** 最近的告警（最近10条） */
  const recentAlerts = computed(() => {
    return alerts.value
      .sort((a, b) => b.receivedAt.getTime() - a.receivedAt.getTime())
      .slice(0, 10)
  })
  
  /** 时间戳精度趋势（最近100个数据点） */
  const precisionTrend = computed(() => {
    return timestampHistory.value
      .slice(-100)
      .map(data => ({
        time: data.receivedAt,
        precision: data.precision
      }))
  })
  
  /** 系统性能趋势（最近100个数据点） */
  const performanceTrend = computed(() => {
    return metricsHistory.value
      .slice(-100)
      .map(data => ({
        time: data.receivedAt,
        cpu: data.cpu_usage || 0,
        memory: data.memory_usage || 0,
        disk: data.disk_usage || 0
      }))
  })
  
  /** 连接质量评分 */
  const connectionQuality = computed(() => {
    if (!isConnected.value) return 0
    
    const stats = wsStats
    const now = Date.now()
    
    // 基于连接时长、消息数量和错误率计算质量评分
    let score = 100
    
    // 连接时长加分（最多20分）
    if (stats.connectedAt) {
      const connectionDuration = now - stats.connectedAt.getTime()
      score += Math.min(20, connectionDuration / (60 * 1000)) // 每分钟1分，最多20分
    }
    
    // 消息接收率加分
    if (stats.messagesReceived > 0) {
      score += Math.min(10, stats.messagesReceived / 100) // 每100条消息1分，最多10分
    }
    
    // 重连次数扣分
    score -= stats.reconnectCount * 5
    
    return Math.max(0, Math.min(100, score))
  })

  // ==================== 操作方法 (Actions) ====================
  
  /**
   * 初始化实时数据连接
   */
  function initializeConnection(): void {
    console.log('🚀 初始化实时数据连接')
    
    // 连接 WebSocket
    connect()
    
    // 设置错误处理
    onError((error) => {
      connectionError.value = `WebSocket 错误: ${error.type}`
      console.error('❌ WebSocket 错误:', error)
    })
    
    // 设置状态变化处理
    onStateChange((state) => {
      console.log(`🔄 WebSocket 状态变化: ${state}`)
      if (state === WebSocketState.CONNECTED) {
        connectionError.value = null
        setupSubscriptions()
      }
    })
  }
  
  /**
   * 设置消息订阅
   */
  function setupSubscriptions(): void {
    console.log('📡 设置消息订阅')
    
    // 订阅实时数据
    subscribeToRealtime((data: RealtimeDataMessage) => {
      handleRealtimeData(data)
      subscriptions.value.realtime = true
    })
    
    // 订阅告警消息
    subscribeToAlerts((data: AlertMessage) => {
      handleAlertMessage(data)
      subscriptions.value.alerts = true
    })
    
    // 订阅性能指标
    subscribeToMetrics((data: any) => {
      handleMetricsData(data)
      subscriptions.value.metrics = true
    })
  }
  
  /**
   * 处理实时数据消息
   */
  function handleRealtimeData(data: RealtimeDataMessage): void {
    dataStats.value.lastDataReceived = new Date()
    
    // 根据主题处理不同类型的数据
    switch (data.topic) {
      case 'timestamp':
        handleTimestampData(data.payload)
        break
      case 'system_event':
        handleSystemEvent(data.payload)
        break
      default:
        console.log('📥 收到未知类型的实时数据:', data)
    }
  }
  
  /**
   * 处理时间戳数据
   */
  function handleTimestampData(payload: any): void {
    const timestampData: TimestampData = {
      timestamp: payload.timestamp,
      nanoseconds: payload.nanoseconds,
      precision: payload.precision,
      source: payload.source,
      receivedAt: new Date()
    }
    
    latestTimestamp.value = timestampData
    timestampHistory.value.push(timestampData)
    dataStats.value.timestampCount++
    
    // 清理过期数据
    cleanupTimestampHistory()
    
    console.log('⏰ 收到时间戳数据:', timestampData)
  }
  
  /**
   * 处理系统事件
   */
  function handleSystemEvent(payload: any): void {
    console.log('🔔 收到系统事件:', payload)
    
    // 可以根据事件类型进行不同的处理
    // 例如：时间源状态变化、系统配置更新等
  }
  
  /**
   * 处理告警消息
   */
  function handleAlertMessage(data: AlertMessage): void {
    const alertData: AlertData = {
      ...data,
      receivedAt: new Date(),
      isRead: false,
      isAcknowledged: false
    }
    
    alerts.value.unshift(alertData) // 添加到列表开头
    dataStats.value.alertCount++
    
    // 清理过期告警
    cleanupAlerts()
    
    console.log(`🚨 收到告警消息 [${data.level}]:`, data.message)
  }
  
  /**
   * 处理性能指标数据
   */
  function handleMetricsData(data: any): void {
    const metricsData: MetricsData = {
      ...data,
      receivedAt: new Date()
    }
    
    latestMetrics.value = metricsData
    metricsHistory.value.push(metricsData)
    dataStats.value.metricsCount++
    
    // 清理过期数据
    cleanupMetricsHistory()
    
    console.log('📊 收到性能指标:', metricsData)
  }
  
  /**
   * 标记告警为已读
   */
  function markAlertAsRead(alertId: string): void {
    const alert = alerts.value.find(a => a.alert_id === alertId)
    if (alert) {
      alert.isRead = true
      console.log(`✅ 告警已标记为已读: ${alertId}`)
    }
  }
  
  /**
   * 确认告警
   */
  function acknowledgeAlert(alertId: string): void {
    const alert = alerts.value.find(a => a.alert_id === alertId)
    if (alert) {
      alert.isAcknowledged = true
      alert.isRead = true
      console.log(`✅ 告警已确认: ${alertId}`)
    }
  }
  
  /**
   * 删除告警
   */
  function removeAlert(alertId: string): void {
    const index = alerts.value.findIndex(a => a.alert_id === alertId)
    if (index !== -1) {
      alerts.value.splice(index, 1)
      console.log(`🗑️ 告警已删除: ${alertId}`)
    }
  }
  
  /**
   * 清除所有告警
   */
  function clearAllAlerts(): void {
    alerts.value = []
    console.log('🗑️ 所有告警已清除')
  }
  
  /**
   * 标记所有告警为已读
   */
  function markAllAlertsAsRead(): void {
    alerts.value.forEach(alert => {
      alert.isRead = true
    })
    console.log('✅ 所有告警已标记为已读')
  }
  
  /**
   * 清理时间戳历史数据
   */
  function cleanupTimestampHistory(): void {
    const now = Date.now()
    const retentionTime = historyConfig.value.retentionTime
    const maxRecords = historyConfig.value.maxRecords
    
    // 按时间清理
    timestampHistory.value = timestampHistory.value.filter(
      data => now - data.receivedAt.getTime() < retentionTime
    )
    
    // 按数量清理
    if (timestampHistory.value.length > maxRecords) {
      timestampHistory.value = timestampHistory.value.slice(-maxRecords)
    }
  }
  
  /**
   * 清理性能指标历史数据
   */
  function cleanupMetricsHistory(): void {
    const now = Date.now()
    const retentionTime = historyConfig.value.retentionTime
    const maxRecords = historyConfig.value.maxRecords
    
    // 按时间清理
    metricsHistory.value = metricsHistory.value.filter(
      data => now - data.receivedAt.getTime() < retentionTime
    )
    
    // 按数量清理
    if (metricsHistory.value.length > maxRecords) {
      metricsHistory.value = metricsHistory.value.slice(-maxRecords)
    }
  }
  
  /**
   * 清理过期告警
   */
  function cleanupAlerts(): void {
    const maxAlerts = 1000 // 最多保留1000条告警
    
    if (alerts.value.length > maxAlerts) {
      // 保留最新的告警，删除最旧的
      alerts.value = alerts.value.slice(0, maxAlerts)
    }
  }
  
  /**
   * 断开连接
   */
  function disconnectWebSocket(): void {
    disconnect()
    subscriptions.value = {
      realtime: false,
      alerts: false,
      metrics: false
    }
    console.log('🔌 WebSocket 连接已断开')
  }
  
  /**
   * 重置所有数据
   */
  function resetAllData(): void {
    latestTimestamp.value = null
    timestampHistory.value = []
    latestMetrics.value = null
    metricsHistory.value = []
    alerts.value = []
    connectionError.value = null
    dataStats.value = {
      timestampCount: 0,
      metricsCount: 0,
      alertCount: 0,
      lastDataReceived: null
    }
    console.log('🔄 所有实时数据已重置')
  }
  
  /**
   * 更新历史记录配置
   */
  function updateHistoryConfig(config: Partial<HistoryConfig>): void {
    historyConfig.value = { ...historyConfig.value, ...config }
    
    // 立即清理数据以应用新配置
    cleanupTimestampHistory()
    cleanupMetricsHistory()
    
    console.log('⚙️ 历史记录配置已更新:', historyConfig.value)
  }

  // ==================== 生命周期管理 ====================
  
  // 组件卸载时断开连接
  onUnmounted(() => {
    disconnectWebSocket()
  })

  // ==================== 返回 Store 接口 ====================
  
  return {
    // WebSocket 相关状态
    wsState,
    wsStats,
    isConnected,
    isConnecting,
    hasConnectionError,
    connectionError,
    connectionQuality,
    subscriptions,
    
    // 数据状态
    latestTimestamp,
    timestampHistory,
    latestMetrics,
    metricsHistory,
    alerts,
    dataStats,
    historyConfig,
    
    // 计算属性
    unreadAlertsCount,
    unacknowledgedAlertsCount,
    alertsByLevel,
    recentAlerts,
    precisionTrend,
    performanceTrend,
    
    // 操作方法
    initializeConnection,
    disconnectWebSocket,
    markAlertAsRead,
    acknowledgeAlert,
    removeAlert,
    clearAllAlerts,
    markAllAlertsAsRead,
    resetAllData,
    updateHistoryConfig
  }
})
