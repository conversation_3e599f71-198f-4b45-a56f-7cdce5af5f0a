/**
 * API 数据类型定义
 * 
 * 定义与后端API交互的所有数据结构，确保类型安全
 * 这些类型与后端的 Rust 结构体保持一致
 */

/**
 * 通用API响应结构
 * 所有API端点都使用这个统一的响应格式
 */
export interface ApiResponse<T> {
  /** 请求是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 错误信息 */
  error?: string
  /** 响应时间戳 */
  timestamp: string
  /** 请求ID（用于追踪） */
  request_id?: string
}

/**
 * 系统状态信息
 * 包含系统运行状态、配置和统计信息
 */
export interface SystemStatus {
  /** 系统状态 */
  status: string
  /** 状态时间戳 */
  timestamp: string
  /** 系统统计信息 */
  statistics: SystemStatistics
  /** 配置信息 */
  config: ConfigData
}

/**
 * 系统统计信息
 * 包含请求统计、性能指标等
 */
export interface SystemStatistics {
  /** 总请求数 */
  total_requests: number
  /** 成功请求数 */
  successful_requests: number
  /** 错误请求数 */
  error_requests: number
  /** 成功率百分比 */
  success_rate_percent: number
  /** 平均响应时间（毫秒） */
  average_response_time_ms: number
  /** 活跃 WebSocket 连接数 */
  active_websocket_connections: number
}

/**
 * 配置数据结构
 * 包含系统的所有配置信息
 */
export interface ConfigData {
  /** 服务器配置 */
  server: ServerConfig
  /** 硬件配置 */
  hardware: HardwareConfig
  /** 网络配置 */
  network: NetworkConfig
  /** 系统配置 */
  system: SystemConfig
}

/**
 * 服务器配置
 */
export interface ServerConfig {
  /** 监听地址 */
  host: string
  /** 监听端口 */
  port: number
  /** 工作线程数 */
  workers: number
}

/**
 * 硬件配置
 */
export interface HardwareConfig {
  /** 主要时间源 */
  primary_source: string
  /** 备用时间源列表 */
  backup_sources: string[]
  /** PPS设备路径 */
  pps_device: string
  /** 串口设备路径 */
  serial_device: string
  /** 串口波特率 */
  serial_baud_rate: number
}

/**
 * 网络配置
 */
export interface NetworkConfig {
  /** NTP服务端口 */
  ntp_port: number
  /** PTP服务端口 */
  ptp_port: number
  /** 允许的客户端网络 */
  allowed_networks: string[]
}

/**
 * 系统配置
 */
export interface SystemConfig {
  /** 日志级别 */
  log_level: string
  /** 数据目录 */
  data_dir: string
  /** 配置文件路径 */
  config_file: string
}

/**
 * 版本信息
 */
export interface VersionInfo {
  /** 应用程序信息 */
  application: ApplicationInfo
  /** 构建信息 */
  build: BuildInfo
  /** 运行时信息 */
  runtime: RuntimeInfo
  /** 依赖信息 */
  dependencies: DependencyInfo
}

/**
 * 应用程序信息
 */
export interface ApplicationInfo {
  /** 应用名称 */
  name: string
  /** 版本号 */
  version: string
  /** 描述 */
  description: string
  /** 作者 */
  authors: string[]
}

/**
 * 构建信息
 */
export interface BuildInfo {
  /** 构建时间戳 */
  timestamp: string
  /** Git提交哈希 */
  git_commit: string
  /** Git分支 */
  git_branch: string
  /** 构建配置 */
  profile: string
  /** 目标架构 */
  target: string
}

/**
 * 运行时信息
 */
export interface RuntimeInfo {
  /** Rust版本 */
  rust_version: string
  /** 操作系统 */
  os: string
  /** 架构 */
  arch: string
  /** 启动时间 */
  started_at: string
  /** 运行时间 */
  uptime_seconds: number
}

/**
 * 依赖信息
 */
export interface DependencyInfo {
  /** Axum版本 */
  axum: string
  /** Tokio版本 */
  tokio: string
  /** Tower版本 */
  tower: string
  /** Serde版本 */
  serde: string
  /** Tracing版本 */
  tracing: string
}

/**
 * 时间源类型枚举
 */
export type TimeSourceType = 'Pps' | 'TenMhz' | 'Tod' | 'Rtc' | 'System' | 'Network'

/**
 * 健康状态枚举
 */
export type HealthStatus = 'Healthy' | 'Warning' | 'Critical' | 'Unknown'

/**
 * 时间源状态信息
 */
export interface TimeSourceStatus {
  /** 时间源类型 */
  source_type: TimeSourceType
  /** 是否处于活跃状态 */
  is_active: boolean
  /** 是否可用 */
  is_available: boolean
  /** 当前精度估计（秒） */
  current_precision: number
  /** 最后一次成功更新的时间戳 */
  last_update: string
  /** 连续错误计数 */
  error_count: number
  /** 质量评分 (0.0-1.0) */
  quality_score: number
  /** 健康状态 */
  health: HealthStatus
  /** 状态描述信息 */
  status_message: string
}

/**
 * 时间源列表
 */
export interface TimeSourceList {
  /** 时间源列表 */
  sources: TimeSource[]
  /** 总数量 */
  total: number
  /** 分页信息 */
  pagination?: PaginationInfo
}

/**
 * 时间源详细信息
 */
export interface TimeSource {
  /** 时间源ID */
  id: string
  /** 时间源名称 */
  name: string
  /** 时间源类型 */
  source_type: TimeSourceType
  /** 状态信息 */
  status: TimeSourceStatus
  /** 配置信息 */
  config: Record<string, any>
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

/**
 * 分页信息
 */
export interface PaginationInfo {
  /** 当前页码 */
  page: number
  /** 每页大小 */
  page_size: number
  /** 总页数 */
  total_pages: number
  /** 总记录数 */
  total_records: number
}

/**
 * 操作结果
 */
export interface OperationResult {
  /** 结果消息 */
  message: string
  /** 操作时间戳 */
  timestamp: string
  /** 是否需要重启 */
  requires_restart?: boolean
}

/**
 * 服务信息
 */
export interface ServiceInfo {
  /** 服务名称 */
  name: string
  /** 服务状态 */
  status: ServiceStatus
  /** 服务描述 */
  description: string
  /** 是否自动启动 */
  auto_start: boolean
  /** 最后启动时间 */
  last_started?: string
  /** 运行时间 */
  uptime_seconds?: number
}

/**
 * 服务状态枚举
 */
export type ServiceStatus = 'Running' | 'Stopped' | 'Failed' | 'Starting' | 'Stopping' | 'Unknown'

/**
 * 服务列表
 */
export interface ServiceList {
  /** 服务列表 */
  services: ServiceInfo[]
  /** 总数量 */
  total: number
}

/**
 * WebSocket消息类型
 */
export interface WebSocketMessage {
  /** 消息类型 */
  type: 'realtime' | 'alert' | 'metrics' | 'status' | 'heartbeat' | 'error'
  /** 消息数据 */
  data?: any
  /** 时间戳 */
  timestamp: string
  /** 消息ID */
  id?: string
}

/**
 * 实时数据消息
 */
export interface RealtimeDataMessage {
  /** 数据主题 */
  topic: string
  /** 数据负载 */
  payload: any
  /** 时间戳 */
  timestamp: string
}

/**
 * 告警消息
 */
export interface AlertMessage {
  /** 告警ID */
  alert_id: string
  /** 告警级别 */
  level: 'info' | 'warning' | 'error' | 'critical'
  /** 组件名称 */
  component: string
  /** 告警消息 */
  message: string
  /** 时间戳 */
  timestamp: string
}
