/**
 * API 客户端
 * 
 * 基于 Axios 的 HTTP 客户端，提供与后端 API 的完整交互功能
 * 包含请求拦截器、响应拦截器、错误处理和重试机制
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import type {
  ApiResponse,
  SystemStatus,
  VersionInfo,
  TimeSourceList,
  TimeSource,
  ServiceList,
  OperationResult
} from './types'

/**
 * API 客户端配置接口
 */
interface ApiClientConfig {
  /** 基础 URL */
  baseURL: string
  /** 请求超时时间（毫秒） */
  timeout: number
  /** 最大重试次数 */
  maxRetries: number
  /** 重试延迟（毫秒） */
  retryDelay: number
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: ApiClientConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  maxRetries: 3,
  retryDelay: 1000
}

/**
 * API 客户端类
 * 
 * 提供完整的 HTTP API 交互功能，包括：
 * - 自动请求/响应拦截
 * - 错误处理和重试机制
 * - 请求取消功能
 * - 统一的响应格式处理
 */
export class ApiClient {
  /** Axios 实例 */
  private readonly axios: AxiosInstance
  /** 客户端配置 */
  private readonly config: ApiClientConfig
  /** 请求取消控制器映射 */
  private readonly cancelTokens = new Map<string, AbortController>()

  /**
   * 构造函数
   * @param config 客户端配置
   */
  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    
    // 创建 Axios 实例
    this.axios = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    // 设置请求拦截器
    this.setupRequestInterceptors()
    
    // 设置响应拦截器
    this.setupResponseInterceptors()
  }

  /**
   * 设置请求拦截器
   * 在请求发送前进行预处理
   */
  private setupRequestInterceptors(): void {
    this.axios.interceptors.request.use(
      (config) => {
        // 添加请求时间戳
        config.metadata = { startTime: Date.now() }
        
        // 添加请求ID用于追踪
        config.headers['X-Request-ID'] = this.generateRequestId()
        
        console.log(`🚀 发送请求: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('❌ 请求配置错误:', error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 设置响应拦截器
   * 处理响应数据和错误
   */
  private setupResponseInterceptors(): void {
    this.axios.interceptors.response.use(
      (response: AxiosResponse) => {
        // 计算请求耗时
        const duration = Date.now() - (response.config.metadata?.startTime || 0)
        console.log(`✅ 请求成功: ${response.config.url} (${duration}ms)`)
        
        return response
      },
      async (error: AxiosError) => {
        return this.handleResponseError(error)
      }
    )
  }

  /**
   * 处理响应错误
   * 包含重试逻辑和错误分类
   */
  private async handleResponseError(error: AxiosError): Promise<never> {
    const config = error.config as any
    
    // 如果没有配置或已达到最大重试次数，直接抛出错误
    if (!config || config.__retryCount >= this.config.maxRetries) {
      console.error('❌ 请求失败:', this.formatError(error))
      throw error
    }

    // 初始化重试计数
    config.__retryCount = config.__retryCount || 0
    config.__retryCount++

    // 判断是否应该重试
    if (this.shouldRetry(error)) {
      console.warn(`🔄 重试请求 (${config.__retryCount}/${this.config.maxRetries}): ${config.url}`)
      
      // 等待重试延迟
      await this.delay(this.config.retryDelay * config.__retryCount)
      
      // 重新发送请求
      return this.axios.request(config)
    }

    throw error
  }

  /**
   * 判断是否应该重试请求
   */
  private shouldRetry(error: AxiosError): boolean {
    // 网络错误或超时错误应该重试
    if (!error.response) {
      return true
    }

    // 5xx 服务器错误应该重试
    const status = error.response.status
    return status >= 500 && status < 600
  }

  /**
   * 格式化错误信息
   */
  private formatError(error: AxiosError): string {
    if (error.response) {
      return `HTTP ${error.response.status}: ${error.response.statusText}`
    } else if (error.request) {
      return '网络连接失败'
    } else {
      return error.message
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 通用 GET 请求方法
   */
  private async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axios.get<ApiResponse<T>>(url, config)
    return response.data
  }

  /**
   * 通用 POST 请求方法
   */
  private async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axios.post<ApiResponse<T>>(url, data, config)
    return response.data
  }

  /**
   * 通用 PUT 请求方法
   */
  private async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axios.put<ApiResponse<T>>(url, data, config)
    return response.data
  }

  /**
   * 通用 DELETE 请求方法
   */
  private async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axios.delete<ApiResponse<T>>(url, config)
    return response.data
  }

  // ==================== 系统 API ====================

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<ApiResponse<SystemStatus>> {
    return this.get<SystemStatus>('/system/status')
  }

  /**
   * 获取版本信息
   */
  async getVersionInfo(): Promise<ApiResponse<VersionInfo>> {
    return this.get<VersionInfo>('/system/version')
  }

  /**
   * 重启系统
   */
  async restartSystem(): Promise<ApiResponse<OperationResult>> {
    return this.post<OperationResult>('/system/restart')
  }

  /**
   * 关闭系统
   */
  async shutdownSystem(): Promise<ApiResponse<OperationResult>> {
    return this.post<OperationResult>('/system/shutdown')
  }

  // ==================== 时间源 API ====================

  /**
   * 获取时间源列表
   */
  async getTimeSources(params?: { page?: number; page_size?: number }): Promise<ApiResponse<TimeSourceList>> {
    return this.get<TimeSourceList>('/time-sources', { params })
  }

  /**
   * 获取时间源详情
   */
  async getTimeSource(id: string): Promise<ApiResponse<TimeSource>> {
    return this.get<TimeSource>(`/time-sources/${id}`)
  }

  /**
   * 创建时间源
   */
  async createTimeSource(data: Partial<TimeSource>): Promise<ApiResponse<TimeSource>> {
    return this.post<TimeSource>('/time-sources', data)
  }

  /**
   * 更新时间源
   */
  async updateTimeSource(id: string, data: Partial<TimeSource>): Promise<ApiResponse<TimeSource>> {
    return this.put<TimeSource>(`/time-sources/${id}`, data)
  }

  /**
   * 删除时间源
   */
  async deleteTimeSource(id: string): Promise<ApiResponse<OperationResult>> {
    return this.delete<OperationResult>(`/time-sources/${id}`)
  }

  // ==================== 服务 API ====================

  /**
   * 获取服务列表
   */
  async getServices(): Promise<ApiResponse<ServiceList>> {
    return this.get<ServiceList>('/services')
  }

  /**
   * 启动服务
   */
  async startService(name: string): Promise<ApiResponse<OperationResult>> {
    return this.post<OperationResult>(`/services/${name}/start`)
  }

  /**
   * 停止服务
   */
  async stopService(name: string): Promise<ApiResponse<OperationResult>> {
    return this.post<OperationResult>(`/services/${name}/stop`)
  }

  /**
   * 重启服务
   */
  async restartService(name: string): Promise<ApiResponse<OperationResult>> {
    return this.post<OperationResult>(`/services/${name}/restart`)
  }

  /**
   * 取消请求
   */
  cancelRequest(requestId: string): void {
    const controller = this.cancelTokens.get(requestId)
    if (controller) {
      controller.abort()
      this.cancelTokens.delete(requestId)
    }
  }

  /**
   * 取消所有请求
   */
  cancelAllRequests(): void {
    this.cancelTokens.forEach(controller => controller.abort())
    this.cancelTokens.clear()
  }
}

/**
 * 默认 API 客户端实例
 */
export const apiClient = new ApiClient()

/**
 * 导出便捷方法
 */
export const api = {
  // 系统相关
  getSystemStatus: () => apiClient.getSystemStatus(),
  getVersionInfo: () => apiClient.getVersionInfo(),
  restartSystem: () => apiClient.restartSystem(),
  shutdownSystem: () => apiClient.shutdownSystem(),
  
  // 时间源相关
  getTimeSources: (params?: { page?: number; page_size?: number }) => apiClient.getTimeSources(params),
  getTimeSource: (id: string) => apiClient.getTimeSource(id),
  createTimeSource: (data: Partial<TimeSource>) => apiClient.createTimeSource(data),
  updateTimeSource: (id: string, data: Partial<TimeSource>) => apiClient.updateTimeSource(id, data),
  deleteTimeSource: (id: string) => apiClient.deleteTimeSource(id),
  
  // 服务相关
  getServices: () => apiClient.getServices(),
  startService: (name: string) => apiClient.startService(name),
  stopService: (name: string) => apiClient.stopService(name),
  restartService: (name: string) => apiClient.restartService(name)
}
