/**
 * WebSocket 客户端
 * 
 * 提供与后端 WebSocket 服务的实时通信功能
 * 支持自动重连、心跳检测、消息订阅和错误处理
 */

import { ref, reactive } from 'vue'
import type { WebSocketMessage, RealtimeDataMessage, AlertMessage } from './types'

/**
 * WebSocket 连接状态枚举
 */
export enum WebSocketState {
  /** 连接中 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 连接关闭 */
  CLOSED = 'closed',
  /** 连接错误 */
  ERROR = 'error'
}

/**
 * WebSocket 客户端配置
 */
interface WebSocketConfig {
  /** WebSocket 服务器 URL */
  url: string
  /** 自动重连 */
  autoReconnect: boolean
  /** 重连间隔（毫秒） */
  reconnectInterval: number
  /** 最大重连次数 */
  maxReconnectAttempts: number
  /** 心跳间隔（毫秒） */
  heartbeatInterval: number
  /** 连接超时（毫秒） */
  connectionTimeout: number
}

/**
 * 消息事件处理器类型
 */
type MessageHandler<T = any> = (data: T) => void

/**
 * 错误事件处理器类型
 */
type ErrorHandler = (error: Event) => void

/**
 * 连接状态变化处理器类型
 */
type StateChangeHandler = (state: WebSocketState) => void

/**
 * WebSocket 客户端类
 * 
 * 功能特性：
 * - 自动重连机制
 * - 心跳检测保持连接活跃
 * - 消息类型订阅系统
 * - 连接状态管理
 * - 错误处理和日志记录
 */
export class WebSocketClient {
  /** WebSocket 实例 */
  private ws: WebSocket | null = null
  
  /** 客户端配置 */
  private readonly config: WebSocketConfig
  
  /** 当前连接状态 */
  public readonly state = ref<WebSocketState>(WebSocketState.CLOSED)
  
  /** 重连尝试次数 */
  private reconnectAttempts = 0
  
  /** 重连定时器 */
  private reconnectTimer: number | null = null
  
  /** 心跳定时器 */
  private heartbeatTimer: number | null = null
  
  /** 消息处理器映射 */
  private readonly messageHandlers = new Map<string, Set<MessageHandler>>()
  
  /** 错误处理器集合 */
  private readonly errorHandlers = new Set<ErrorHandler>()
  
  /** 状态变化处理器集合 */
  private readonly stateChangeHandlers = new Set<StateChangeHandler>()
  
  /** 连接统计信息 */
  public readonly stats = reactive({
    /** 连接时间 */
    connectedAt: null as Date | null,
    /** 接收消息数 */
    messagesReceived: 0,
    /** 发送消息数 */
    messagesSent: 0,
    /** 重连次数 */
    reconnectCount: 0,
    /** 最后一次心跳时间 */
    lastHeartbeat: null as Date | null
  })

  /**
   * 构造函数
   * @param config WebSocket 配置
   */
  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: import.meta.env.VITE_WS_URL || 'ws://localhost:3000/ws/realtime',
      autoReconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      connectionTimeout: 10000,
      ...config
    }
  }

  /**
   * 建立 WebSocket 连接
   */
  connect(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.warn('⚠️ WebSocket 已经连接')
      return
    }

    console.log(`🔌 连接 WebSocket: ${this.config.url}`)
    this.setState(WebSocketState.CONNECTING)

    try {
      this.ws = new WebSocket(this.config.url)
      this.setupEventHandlers()
      
      // 设置连接超时
      setTimeout(() => {
        if (this.state.value === WebSocketState.CONNECTING) {
          console.error('❌ WebSocket 连接超时')
          this.ws?.close()
          this.setState(WebSocketState.ERROR)
        }
      }, this.config.connectionTimeout)
      
    } catch (error) {
      console.error('❌ WebSocket 连接失败:', error)
      this.setState(WebSocketState.ERROR)
      this.handleReconnect()
    }
  }

  /**
   * 关闭 WebSocket 连接
   */
  disconnect(): void {
    console.log('🔌 断开 WebSocket 连接')
    
    // 清理定时器
    this.clearTimers()
    
    // 关闭连接
    if (this.ws) {
      this.ws.close(1000, '客户端主动断开')
      this.ws = null
    }
    
    this.setState(WebSocketState.CLOSED)
    this.resetStats()
  }

  /**
   * 发送消息
   */
  send(message: any): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ WebSocket 未连接，无法发送消息')
      return false
    }

    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
      this.ws.send(messageStr)
      this.stats.messagesSent++
      console.log('📤 发送消息:', message)
      return true
    } catch (error) {
      console.error('❌ 发送消息失败:', error)
      return false
    }
  }

  /**
   * 订阅消息类型
   */
  subscribe<T = any>(messageType: string, handler: MessageHandler<T>): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set())
    }
    
    this.messageHandlers.get(messageType)!.add(handler)
    console.log(`📝 订阅消息类型: ${messageType}`)
    
    // 返回取消订阅函数
    return () => {
      this.unsubscribe(messageType, handler)
    }
  }

  /**
   * 取消订阅消息类型
   */
  unsubscribe<T = any>(messageType: string, handler: MessageHandler<T>): void {
    const handlers = this.messageHandlers.get(messageType)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.messageHandlers.delete(messageType)
      }
      console.log(`📝 取消订阅消息类型: ${messageType}`)
    }
  }

  /**
   * 监听错误事件
   */
  onError(handler: ErrorHandler): () => void {
    this.errorHandlers.add(handler)
    return () => this.errorHandlers.delete(handler)
  }

  /**
   * 监听状态变化
   */
  onStateChange(handler: StateChangeHandler): () => void {
    this.stateChangeHandlers.add(handler)
    return () => this.stateChangeHandlers.delete(handler)
  }

  /**
   * 设置 WebSocket 事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.ws) return

    // 连接打开事件
    this.ws.onopen = () => {
      console.log('✅ WebSocket 连接成功')
      this.setState(WebSocketState.CONNECTED)
      this.stats.connectedAt = new Date()
      this.reconnectAttempts = 0
      this.startHeartbeat()
    }

    // 接收消息事件
    this.ws.onmessage = (event) => {
      this.handleMessage(event.data)
    }

    // 连接关闭事件
    this.ws.onclose = (event) => {
      console.log(`🔌 WebSocket 连接关闭: ${event.code} - ${event.reason}`)
      this.setState(WebSocketState.CLOSED)
      this.clearTimers()
      
      // 如果不是主动关闭且启用自动重连，则尝试重连
      if (event.code !== 1000 && this.config.autoReconnect) {
        this.handleReconnect()
      }
    }

    // 连接错误事件
    this.ws.onerror = (error) => {
      console.error('❌ WebSocket 错误:', error)
      this.setState(WebSocketState.ERROR)
      this.errorHandlers.forEach(handler => handler(error))
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: string): void {
    try {
      const message: WebSocketMessage = JSON.parse(data)
      this.stats.messagesReceived++
      
      console.log('📥 接收消息:', message)
      
      // 处理心跳消息
      if (message.type === 'heartbeat') {
        this.stats.lastHeartbeat = new Date()
        return
      }
      
      // 分发消息给订阅者
      const handlers = this.messageHandlers.get(message.type)
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message.data)
          } catch (error) {
            console.error(`❌ 消息处理器错误 (${message.type}):`, error)
          }
        })
      }
      
    } catch (error) {
      console.error('❌ 解析消息失败:', error, data)
    }
  }

  /**
   * 处理重连逻辑
   */
  private handleReconnect(): void {
    if (!this.config.autoReconnect || this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('❌ 达到最大重连次数，停止重连')
      return
    }

    this.reconnectAttempts++
    this.stats.reconnectCount++
    
    console.log(`🔄 准备重连 (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`)
    
    this.reconnectTimer = window.setTimeout(() => {
      this.connect()
    }, this.config.reconnectInterval)
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = window.setInterval(() => {
      this.send({
        type: 'heartbeat',
        timestamp: new Date().toISOString()
      })
    }, this.config.heartbeatInterval)
  }

  /**
   * 设置连接状态
   */
  private setState(newState: WebSocketState): void {
    if (this.state.value !== newState) {
      this.state.value = newState
      this.stateChangeHandlers.forEach(handler => handler(newState))
    }
  }

  /**
   * 清理定时器
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 重置统计信息
   */
  private resetStats(): void {
    this.stats.connectedAt = null
    this.stats.messagesReceived = 0
    this.stats.messagesSent = 0
    this.stats.lastHeartbeat = null
  }

  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.state.value === WebSocketState.CONNECTED
  }

  /**
   * 获取连接时长（毫秒）
   */
  get connectionDuration(): number {
    return this.stats.connectedAt ? Date.now() - this.stats.connectedAt.getTime() : 0
  }
}

/**
 * 默认 WebSocket 客户端实例
 */
export const wsClient = new WebSocketClient()

/**
 * 便捷的消息订阅方法
 */
export const useWebSocket = () => {
  return {
    client: wsClient,
    state: wsClient.state,
    stats: wsClient.stats,
    
    // 连接管理
    connect: () => wsClient.connect(),
    disconnect: () => wsClient.disconnect(),
    
    // 消息订阅
    subscribeToRealtime: (handler: MessageHandler<RealtimeDataMessage>) => 
      wsClient.subscribe('realtime', handler),
    subscribeToAlerts: (handler: MessageHandler<AlertMessage>) => 
      wsClient.subscribe('alert', handler),
    subscribeToMetrics: (handler: MessageHandler<any>) => 
      wsClient.subscribe('metrics', handler),
    
    // 事件监听
    onError: (handler: ErrorHandler) => wsClient.onError(handler),
    onStateChange: (handler: StateChangeHandler) => wsClient.onStateChange(handler)
  }
}
