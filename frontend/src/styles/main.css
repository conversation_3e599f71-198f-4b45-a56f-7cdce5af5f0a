@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply antialiased;
  }
  
  body {
    @apply bg-slate-950 text-slate-100 font-sans;
    @apply overflow-hidden;
  }
  
  * {
    @apply border-slate-700;
  }
}

@layer components {
  /* Glass morphism base styles */
  .glass {
    @apply backdrop-blur-xl bg-white/5 border border-white/10;
    @apply shadow-glass;
  }
  
  .glass-strong {
    @apply backdrop-blur-2xl bg-white/10 border border-white/20;
    @apply shadow-glass;
  }
  
  .glass-subtle {
    @apply backdrop-blur-lg bg-white/[0.02] border border-white/5;
    @apply shadow-glass;
  }
  
  /* Apple-style buttons */
  .btn-apple {
    @apply px-4 py-2 rounded-apple font-medium transition-all duration-200;
    @apply glass hover:bg-white/10 active:scale-95;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/50;
  }
  
  .btn-apple-primary {
    @apply btn-apple bg-blue-600 hover:bg-blue-700 text-white;
    @apply shadow-apple-lg;
  }
  
  /* Apple-style inputs */
  .input-apple {
    @apply w-full px-4 py-3 rounded-apple;
    @apply glass placeholder-slate-400;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/50;
    @apply transition-all duration-200;
  }
  
  /* Status indicators */
  .status-healthy {
    @apply text-green-400 bg-green-400/10 border-green-400/20;
  }
  
  .status-warning {
    @apply text-yellow-400 bg-yellow-400/10 border-yellow-400/20;
  }
  
  .status-error {
    @apply text-red-400 bg-red-400/10 border-red-400/20;
  }
  
  /* Scrollbar styling */
  .scrollbar-apple {
    scrollbar-width: thin;
    scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
  }
  
  .scrollbar-apple::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-apple::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-apple::-webkit-scrollbar-thumb {
    background-color: rgba(148, 163, 184, 0.3);
    border-radius: 3px;
  }
  
  .scrollbar-apple::-webkit-scrollbar-thumb:hover {
    background-color: rgba(148, 163, 184, 0.5);
  }
}

@layer utilities {
  /* Custom backdrop filters */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  .backdrop-blur-4xl {
    backdrop-filter: blur(72px);
  }
  
  .backdrop-blur-5xl {
    backdrop-filter: blur(96px);
  }
  
  /* Text gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent;
  }
  
  /* Glow effects */
  .glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }
  
  .glow-yellow {
    box-shadow: 0 0 20px rgba(234, 179, 8, 0.3);
  }
  
  .glow-red {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }
}
