# 时间同步系统前端生产环境配置
# 
# 这个文件包含了生产环境的特定配置
# 请根据实际部署环境修改相应的值

# 应用基本信息
VITE_APP_TITLE=时间同步系统监控台
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=高精度时间同步系统监控和管理平台

# API 服务配置（生产环境）
# 请修改为实际的生产环境 API 地址
VITE_API_BASE_URL=https://your-production-domain.com/api

# WebSocket 服务配置（生产环境）
# 请修改为实际的生产环境 WebSocket 地址
VITE_WS_URL=wss://your-production-domain.com/ws/realtime

# 生产环境配置
# 禁用开发工具
VITE_DEV_TOOLS=false

# 禁用详细日志
VITE_VERBOSE_LOGGING=false

# 启用性能监控
VITE_PERFORMANCE_MONITORING=true

# 功能开关（生产环境）
VITE_ENABLE_REALTIME=true
VITE_ENABLE_ALERTS=true
VITE_ENABLE_CHARTS=true
VITE_ENABLE_DARK_THEME=true

# 数据刷新配置（生产环境优化）
VITE_STATUS_REFRESH_INTERVAL=60000
VITE_TIMESOURCE_REFRESH_INTERVAL=30000

# WebSocket 重连配置（生产环境）
VITE_WS_MAX_RECONNECT_ATTEMPTS=5
VITE_WS_RECONNECT_INTERVAL=5000
VITE_WS_HEARTBEAT_INTERVAL=60000

# UI 配置
VITE_DEFAULT_LOCALE=zh-CN
VITE_DEFAULT_TIMEZONE=Asia/Shanghai
VITE_TIMESTAMP_FORMAT=YYYY-MM-DD HH:mm:ss.SSS
VITE_NUMBER_PRECISION=3

# 监控配置（生产环境）
VITE_ENABLE_ERROR_MONITORING=true
VITE_ERROR_MONITORING_URL=https://your-monitoring-service.com/errors

VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_PERFORMANCE_MONITORING_URL=https://your-monitoring-service.com/performance

# 安全配置（生产环境）
VITE_API_TIMEOUT=15000
VITE_ENABLE_HTTPS=true

# 缓存配置（生产环境）
VITE_ENABLE_LOCAL_CACHE=true
VITE_CACHE_EXPIRY=600000

# 调试配置（生产环境）
VITE_SHOW_DEBUG_INFO=false
VITE_ENABLE_VUE_DEVTOOLS=false
