#!/bin/bash

# 时间同步系统前端构建脚本
# 
# 功能：
# - 安装依赖
# - 类型检查
# - 代码格式化
# - 构建生产版本
# - 生成构建报告

set -e  # 遇到错误立即退出

echo "🚀 开始构建时间同步系统前端..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node_version=$(node -v)
echo "Node.js 版本: $node_version"

# 检查 npm 版本
npm_version=$(npm -v)
echo "npm 版本: $npm_version"

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 代码格式化
echo "🎨 格式化代码..."
npm run format

# 代码检查
echo "🔍 检查代码质量..."
npm run lint

# TypeScript 类型检查
echo "🔧 TypeScript 类型检查..."
npx vue-tsc --noEmit

# 运行测试（如果有）
if [ -d "src/__tests__" ] || [ -d "tests" ]; then
    echo "🧪 运行测试..."
    npm run test
fi

# 构建生产版本
echo "🏗️ 构建生产版本..."
npm run build

# 检查构建结果
if [ -d "dist" ]; then
    echo "✅ 构建成功！"
    echo "📊 构建统计："
    
    # 显示构建文件大小
    if command -v du &> /dev/null; then
        echo "总大小: $(du -sh dist | cut -f1)"
    fi
    
    # 显示主要文件
    echo "主要文件："
    find dist -name "*.js" -o -name "*.css" -o -name "*.html" | head -10
    
    echo ""
    echo "🎉 前端构建完成！"
    echo "📁 构建文件位于: ./dist/"
    echo "🌐 可以使用 'npm run preview' 预览构建结果"
    
else
    echo "❌ 构建失败！dist 目录不存在"
    exit 1
fi
