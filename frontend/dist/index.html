<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原子钟授时服务器系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #007AFF 0%, #AF52DE 50%, #FF2D92 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .card h2 {
            margin-bottom: 16px;
            font-size: 1.5rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: 500;
        }

        .status-value {
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 600;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
        }

        .success {
            color: #51cf66;
            background: rgba(81, 207, 102, 0.1);
            border: 1px solid rgba(81, 207, 102, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
        }

        .ws-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .ws-connected {
            background: #51cf66;
            box-shadow: 0 0 8px rgba(81, 207, 102, 0.5);
        }

        .ws-disconnected {
            background: #ff6b6b;
        }

        pre {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }

        .time-display {
            font-size: 2rem;
            font-family: 'Monaco', 'Menlo', monospace;
            text-align: center;
            margin: 20px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a1d29 50%, #2d3748 100%);
            min-height: 100vh;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* 登录界面样式 */
        .login-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0f1419 0%, #1a1d29 50%, #2d3748 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-box {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #ffffff;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .logo p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-input:focus {
            outline: none;
            border-color: #4299e1;
            background: rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .login-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(66, 153, 225, 0.3);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-message {
            margin-top: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            text-align: center;
        }

        .status-success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .status-error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* 页面内容样式 */
        .page-content {
            display: block;
        }

        .page-content.hidden {
            display: none;
        }

        /* 主应用界面样式 */
        .app-container {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #0f1419 0%, #1a1d29 50%, #2d3748 100%);
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-title {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 8px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: 14px;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.08);
            color: #ffffff;
        }

        .nav-link.active {
            background: rgba(66, 153, 225, 0.2);
            color: #4299e1;
            border: 1px solid rgba(66, 153, 225, 0.3);
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 16px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .page-title {
            color: #ffffff;
            font-size: 24px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .current-time {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        .logout-btn {
            padding: 8px 16px;
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 6px;
            color: #ef4444;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(239, 68, 68, 0.3);
        }

        /* 卡片网格布局 */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .card-title {
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .card-icon {
            margin-right: 8px;
            font-size: 18px;
        }

        .card-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-online {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .status-warning {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }

        .status-error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .card-content {
            color: rgba(255, 255, 255, 0.8);
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .metric-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .metric-value {
            color: #ffffff;
            font-weight: 600;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        .metric-value.large {
            font-size: 24px;
            color: #4299e1;
        }

        .metric-value.success {
            color: #22c55e;
        }

        .metric-value.warning {
            color: #fbbf24;
        }

        .metric-value.error {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <!-- 登录界面 -->
    <div id="loginContainer" class="login-container">
        <div class="login-box">
            <div class="logo">
                <h1>🕐 高精度授时服务器</h1>
                <p>专业时间同步系统</p>
            </div>
            <form id="loginForm">
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" id="username" class="form-input" placeholder="请输入用户名" required>
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="password" class="form-input" placeholder="请输入密码" required>
                </div>
                <button type="submit" id="loginBtn" class="login-button">登录</button>
                <div id="statusMessage" class="status-message hidden"></div>
            </form>
            <div class="footer">
                © 2024 高精度授时服务器 | 版本 1.0.0
            </div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="appContainer" class="app-container hidden">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">SWM安全授时服务器</div>
                <div class="sidebar-subtitle">系统状态仪表盘</div>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <button class="nav-link active" data-page="dashboard">
                        <span class="nav-icon">📊</span>
                        仪表盘
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="gnss">
                        <span class="nav-icon">🛰️</span>
                        GNSS 接收
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="ptp">
                        <span class="nav-icon">📡</span>
                        PTP 设备
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="ntp">
                        <span class="nav-icon">🌐</span>
                        NTP 设备
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="tod">
                        <span class="nav-icon">🕰️</span>
                        TOD 设备
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="signal">
                        <span class="nav-icon">📶</span>
                        信号输出
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="port">
                        <span class="nav-icon">🔌</span>
                        端口管理
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="network">
                        <span class="nav-icon">🌐</span>
                        网络配置
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="maintenance">
                        <span class="nav-icon">🔧</span>
                        维护管理
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="user">
                        <span class="nav-icon">👤</span>
                        用户安全
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="backup">
                        <span class="nav-icon">💾</span>
                        备份与恢复
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-page="system">
                        <span class="nav-icon">⚙️</span>
                        系统日志
                    </button>
                </li>
            </ul>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <h1 class="page-title" id="pageTitle">系统状态仪表盘</h1>
                <div class="header-actions">
                    <div class="current-time" id="currentTime">2025-07-25 23:48:37 +9.1</div>
                    <button class="logout-btn" id="logoutBtn">退出登录</button>
                </div>
            </div>

            <!-- 页面内容容器 -->
            <div id="pageContent">
                <!-- 仪表盘页面 -->
                <div id="dashboardPage" class="page-content">
                    <div class="cards-grid">
                        <!-- 原子钟状态卡片 -->
                        <div class="card" data-click="atomic-clock">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">⚛️</span>
                                    原子钟状态
                                </div>
                                <div class="card-status status-online">已同步</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">核心温度</span>
                                    <span class="metric-value">56.4°C</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">磁场强度</span>
                                    <span class="metric-value success">已锁定</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">信号强度</span>
                                    <span class="metric-value">-45 dBm</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">运行时间</span>
                                    <span class="metric-value">247天 12小时</span>
                                </div>
                            </div>
                        </div>

                        <!-- GNSS 接收器卡片 -->
                        <div class="card" data-click="gnss-receiver">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">🛰️</span>
                                    GNSS 接收器
                                </div>
                                <div class="card-status status-online">正常</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">可见卫星</span>
                                    <span class="metric-value">24</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">跟踪卫星</span>
                                    <span class="metric-value">17</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">定位精度</span>
                                    <span class="metric-value">2.1m</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">时间精度</span>
                                    <span class="metric-value success">±12ns</span>
                                </div>
                            </div>
                        </div>

                        <!-- PTP 服务卡片 -->
                        <div class="card" data-click="ptp-service">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">📡</span>
                                    PTP 服务
                                </div>
                                <div class="card-status status-online">主时钟</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">主时钟精度</span>
                                    <span class="metric-value success">±50ns</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">从时钟数量</span>
                                    <span class="metric-value">10</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">同步精度</span>
                                    <span class="metric-value success">±50ns</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">网络延迟</span>
                                    <span class="metric-value">125μs</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- GNSS 接收页面 -->
                <div id="gnssPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">🛰️</span>
                                    GNSS 接收器状态
                                </div>
                                <div class="card-status status-online">正常运行</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">接收器型号</span>
                                    <span class="metric-value">Trimble Thunderbolt E</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">固件版本</span>
                                    <span class="metric-value">2.43</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">天线状态</span>
                                    <span class="metric-value success">正常</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">信号质量</span>
                                    <span class="metric-value success">优秀</span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">📡</span>
                                    卫星信息
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">GPS 卫星</span>
                                    <span class="metric-value">12 颗</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">GLONASS 卫星</span>
                                    <span class="metric-value">8 颗</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">北斗卫星</span>
                                    <span class="metric-value">6 颗</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Galileo 卫星</span>
                                    <span class="metric-value">4 颗</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- PTP 设备页面 -->
                <div id="ptpPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">📡</span>
                                    PTP 主时钟
                                </div>
                                <div class="card-status status-online">运行中</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">时钟 ID</span>
                                    <span class="metric-value">00:1B:21:FF:FE:12:34:56</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">域号</span>
                                    <span class="metric-value">0</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">优先级1</span>
                                    <span class="metric-value">128</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">优先级2</span>
                                    <span class="metric-value">128</span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">🔗</span>
                                    从时钟列表
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">从时钟 1</span>
                                    <span class="metric-value success">同步中</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">从时钟 2</span>
                                    <span class="metric-value success">同步中</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">从时钟 3</span>
                                    <span class="metric-value warning">延迟高</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">从时钟 4</span>
                                    <span class="metric-value error">离线</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- NTP 设备页面 -->
                <div id="ntpPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">🌐</span>
                                    NTP 服务器
                                </div>
                                <div class="card-status status-online">运行中</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">服务端口</span>
                                    <span class="metric-value">123</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">层级</span>
                                    <span class="metric-value">1</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">精度</span>
                                    <span class="metric-value success">±1ms</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">客户端数量</span>
                                    <span class="metric-value">156</span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">📊</span>
                                    请求统计
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">今日请求</span>
                                    <span class="metric-value">1,234,567</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">平均响应时间</span>
                                    <span class="metric-value">0.5ms</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">成功率</span>
                                    <span class="metric-value success">99.98%</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">错误请求</span>
                                    <span class="metric-value">247</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- TOD 设备页面 -->
                <div id="todPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">🕰️</span>
                                    TOD 输出
                                </div>
                                <div class="card-status status-online">正常</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">输出格式</span>
                                    <span class="metric-value">IRIG-B</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">编码方式</span>
                                    <span class="metric-value">DC Level Shift</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">输出电平</span>
                                    <span class="metric-value">TTL</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">精度</span>
                                    <span class="metric-value success">±1μs</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 信号输出页面 -->
                <div id="signalPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">📶</span>
                                    频率输出
                                </div>
                                <div class="card-status status-online">正常</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">输出频率</span>
                                    <span class="metric-value">10 MHz</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">频率稳定度</span>
                                    <span class="metric-value success">1×10⁻¹²</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">输出功率</span>
                                    <span class="metric-value">+13 dBm</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">负载阻抗</span>
                                    <span class="metric-value">50Ω</span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">⚡</span>
                                    脉冲输出
                                </div>
                                <div class="card-status status-online">正常</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">脉冲频率</span>
                                    <span class="metric-value">1 PPS</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">脉冲宽度</span>
                                    <span class="metric-value">100ms</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">上升时间</span>
                                    <span class="metric-value">10ns</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">精度</span>
                                    <span class="metric-value success">±10ns</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 端口管理页面 -->
                <div id="portPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">🔌</span>
                                    以太网端口
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">端口 1</span>
                                    <span class="metric-value success">1000M 全双工</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">端口 2</span>
                                    <span class="metric-value success">1000M 全双工</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">端口 3</span>
                                    <span class="metric-value warning">100M 全双工</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">端口 4</span>
                                    <span class="metric-value error">断开</span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">📡</span>
                                    串行端口
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">RS232-1</span>
                                    <span class="metric-value success">9600 bps</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">RS232-2</span>
                                    <span class="metric-value success">115200 bps</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">RS485-1</span>
                                    <span class="metric-value success">38400 bps</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">RS485-2</span>
                                    <span class="metric-value">未使用</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 网络配置页面 -->
                <div id="networkPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">🌐</span>
                                    网络接口
                                </div>
                                <div class="card-status status-online">已连接</div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">IP 地址</span>
                                    <span class="metric-value">*************</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">子网掩码</span>
                                    <span class="metric-value">*************</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">网关</span>
                                    <span class="metric-value">***********</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">DNS</span>
                                    <span class="metric-value">*******</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 维护管理页面 -->
                <div id="maintenancePage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">🔧</span>
                                    系统维护
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">系统版本</span>
                                    <span class="metric-value">v2.1.3</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">上次维护</span>
                                    <span class="metric-value">2024-07-15</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">下次维护</span>
                                    <span class="metric-value">2024-10-15</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">维护状态</span>
                                    <span class="metric-value success">正常</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户安全页面 -->
                <div id="userPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">👤</span>
                                    用户管理
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">当前用户</span>
                                    <span class="metric-value">admin</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">用户角色</span>
                                    <span class="metric-value">管理员</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">登录时间</span>
                                    <span class="metric-value">2025-08-10 11:54:55</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">会话状态</span>
                                    <span class="metric-value success">活跃</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 备份与恢复页面 -->
                <div id="backupPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">💾</span>
                                    配置备份
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">最新备份</span>
                                    <span class="metric-value">2025-08-10 02:00:00</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">备份大小</span>
                                    <span class="metric-value">2.3 MB</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">备份数量</span>
                                    <span class="metric-value">30</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">自动备份</span>
                                    <span class="metric-value success">已启用</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统日志页面 -->
                <div id="systemPage" class="page-content hidden">
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <span class="card-icon">⚙️</span>
                                    系统日志
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="metric-row">
                                    <span class="metric-label">日志级别</span>
                                    <span class="metric-value">INFO</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">日志大小</span>
                                    <span class="metric-value">156 MB</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">今日条目</span>
                                    <span class="metric-value">12,456</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">错误数量</span>
                                    <span class="metric-value warning">3</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // WebSocket 连接
        let ws = null;
        let isLoggedIn = false;

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            // 绑定登录表单事件
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleLogin);

            // 绑定退出登录事件
            const logoutBtn = document.getElementById('logoutBtn');
            logoutBtn.addEventListener('click', handleLogout);

            // 绑定导航事件
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', handleNavigation);
            });

            // 绑定卡片点击事件
            const cards = document.querySelectorAll('[data-click]');
            cards.forEach(card => {
                card.addEventListener('click', handleCardClick);
            });

            // 启动时间更新
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
        }

        // 处理登录
        async function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');

            // 显示加载状态
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<span class="loading"></span>登录中...';

            try {
                // 模拟登录验证
                await new Promise(resolve => setTimeout(resolve, 1500));

                if (username === 'admin' && password === 'admin') {
                    // 登录成功
                    showStatusMessage('登录成功！', 'success');
                    setTimeout(() => {
                        showMainApp();
                        connectWebSocket();
                    }, 1000);
                } else {
                    throw new Error('用户名或密码错误');
                }
            } catch (error) {
                showStatusMessage(error.message, 'error');
                loginBtn.disabled = false;
                loginBtn.innerHTML = '登录';
            }
        }

        // 显示状态消息
        function showStatusMessage(message, type) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = message;
            statusMessage.className = `status-message status-${type}`;
            statusMessage.classList.remove('hidden');
        }

        // 显示主应用界面
        function showMainApp() {
            document.getElementById('loginContainer').classList.add('hidden');
            document.getElementById('appContainer').classList.remove('hidden');
            isLoggedIn = true;
        }

        // 处理退出登录
        function handleLogout() {
            if (ws) {
                ws.close();
                ws = null;
            }

            document.getElementById('appContainer').classList.add('hidden');
            document.getElementById('loginContainer').classList.remove('hidden');
            document.getElementById('statusMessage').classList.add('hidden');
            document.getElementById('loginForm').reset();
            isLoggedIn = false;
        }

        // 处理导航
        function handleNavigation(event) {
            const clickedLink = event.currentTarget;
            const page = clickedLink.dataset.page;

            // 发送点击事件到后端
            sendClickEvent('navigation', page, clickedLink.textContent.trim());

            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            clickedLink.classList.add('active');

            // 切换页面内容
            switchPage(page);

            console.log(`导航到页面: ${page}`);
        }

        // 切换页面内容
        function switchPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.add('hidden');
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId + 'Page');
            if (targetPage) {
                targetPage.classList.remove('hidden');
            }

            // 更新页面标题
            const titles = {
                'dashboard': '系统状态仪表盘',
                'gnss': 'GNSS 接收器管理',
                'ptp': 'PTP 设备管理',
                'ntp': 'NTP 设备管理',
                'tod': 'TOD 设备管理',
                'signal': '信号输出管理',
                'port': '端口管理',
                'network': '网络配置',
                'maintenance': '维护管理',
                'user': '用户安全管理',
                'backup': '备份与恢复',
                'system': '系统日志'
            };

            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle && titles[pageId]) {
                pageTitle.textContent = titles[pageId];
            }
        }

        // 处理卡片点击
        function handleCardClick(event) {
            const card = event.currentTarget;
            const clickType = card.dataset.click;
            const cardTitle = card.querySelector('.card-title').textContent.trim();

            // 发送点击事件到后端
            sendClickEvent('card', clickType, cardTitle);

            // 添加点击效果
            card.style.transform = 'translateY(-4px)';
            setTimeout(() => {
                card.style.transform = 'translateY(-2px)';
            }, 150);

            console.log(`点击卡片: ${clickType} - ${cardTitle}`);
        }

        // 发送点击事件到后端
        function sendClickEvent(type, target, description) {
            const eventData = {
                type: 'user_click',
                timestamp: new Date().toISOString(),
                click_type: type,
                target: target,
                description: description,
                user_agent: navigator.userAgent,
                page_url: window.location.href
            };

            // 通过 WebSocket 发送
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(eventData));
            }

            // 同时通过 HTTP API 发送（备用）
            fetch('/api/events/click', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(eventData)
            }).catch(error => {
                console.warn('Failed to send click event via HTTP:', error);
            });
        }

        // 连接 WebSocket
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                console.log('WebSocket 连接已建立');
                sendClickEvent('system', 'websocket_connect', 'WebSocket连接建立');
            };

            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                } catch (error) {
                    console.error('解析 WebSocket 消息失败:', error);
                }
            };

            ws.onclose = function() {
                console.log('WebSocket 连接已关闭');
                if (isLoggedIn) {
                    setTimeout(connectWebSocket, 3000);
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket 错误:', error);
            };
        }

        // 处理 WebSocket 消息
        function handleWebSocketMessage(data) {
            console.log('收到 WebSocket 消息:', data);
        }

        // 更新当前时间显示
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }) + ' ****';

            document.getElementById('currentTime').textContent = timeString;
        }
    </script>
</body>
</html>