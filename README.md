# 原子钟授时服务器 (Atomic Clock Time Server)

企业级高精度原子钟授时服务器，提供纳秒级时间同步服务，支持多种硬件时间源和网络时间协议。

## 🚀 核心特性

### 时间同步技术
- **多源时间融合**: 智能融合 PPS、10MHz、TOD、RTC 和系统时钟源
- **高精度同步算法**: 自适应 PLL、FLL、扩展卡尔曼滤波器
- **纳秒级精度**: 支持亚微秒级时间戳生成和同步
- **故障自动切换**: 智能时间源选择和故障转移机制

### 硬件支持
- **PPS 信号处理**: Linux PPS 子系统集成，支持热插拔检测
- **10MHz 参考时钟**: 频率测量、相位检测、抖动分析
- **TOD 串行数据**: NMEA 协议解析，GPS/北斗时间源支持
- **RTC 管理**: DS3231 等高精度 RTC 芯片，温度补偿

### 服务集成
- **chrony 深度集成**: 配置生成、服务管理、健康监控、性能分析
- **linuxptp 完整支持**: PTP协议栈、端口状态、路径延迟统计
- **智能服务协调**: 资源冲突检测、端口管理、启动顺序控制
- **统一配置管理**: 热重载、配置验证、一致性检查
- **故障检测恢复**: 自动故障检测、服务重启、性能监控

### 现代化界面
- **Vue3 + TypeScript**: 组合式API，完整类型安全
- **Glassmorphism 设计**: 7个核心组件，Apple风格UI系统
- **实时监控**: 4路WebSocket实时数据流 (realtime/alerts/metrics/status)
- **智能缓存**: HTTP客户端请求缓存、去重、重试机制
- **无障碍性**: WCAG 2.1 AA标准，完整键盘导航支持
- **响应式设计**: 移动端优先，多设备完美适配
- **主题系统**: 浅色/深色/系统主题，动态切换

### 企业级特性
- **高可用性**: 多重冗余和故障恢复机制
- **监控告警**: 完整的健康监控和告警系统
- **配置管理**: 热重载配置，无需重启服务
- **交叉编译**: 支持 LoongArch64 (龙芯) 目标平台

## 📁 项目结构

```
atomic-clock-server/
├── backend/                 # 后端 Rust 项目
│   └── crates/              # Cargo 工作空间
│       ├── server/          # 🌐 主 API 服务器应用
│       ├── time-core/       # ⏰ 核心时间同步引擎
│       │   ├── src/
│       │   │   ├── sync_engine.rs      # PLL/FLL/Kalman 算法
│       │   │   ├── precision_evaluator.rs # 精度评估
│       │   │   ├── timestamp_generator.rs # 时间戳生成
│       │   │   ├── fault_detector.rs      # 故障检测
│       │   │   └── failover_manager.rs    # 故障转移
│       │   ├── benches/     # 性能基准测试
│       │   └── tests/       # 单元测试
│       ├── hardware/        # 🔧 硬件抽象层
│       │   ├── src/
│       │   │   ├── pps.rs   # PPS 信号处理和质量评估
│       │   │   ├── ten_mhz.rs # 10MHz 频率分析和 FLL
│       │   │   ├── tod.rs   # TOD/NMEA 协议解析
│       │   │   ├── rtc.rs   # RTC 管理和温度补偿
│       │   │   └── platform.rs # 跨平台抽象
│       │   ├── benches/     # 硬件性能测试
│       │   └── tests/       # 集成测试和错误注入
│       ├── service-integration/ # 🔗 服务集成
│       │   ├── src/
│       │   │   ├── chrony.rs    # chrony 完整集成
│       │   │   ├── linuxptp.rs  # linuxptp 管理
│       │   │   └── coordinator.rs # 服务协调器
│       │   ├── templates/   # 配置文件模板
│       │   └── tests/       # 服务集成测试
│       ├── config/          # ⚙️ 配置管理
│       └── types/           # 📦 共享类型和错误定义
├── frontend/                # 前端 Vue3 项目
│   ├── src/
│   │   ├── components/      # Vue 组件
│   │   │   ├── glass/       # Glassmorphism 组件库
│   │   │   │   ├── GlassCard.vue
│   │   │   │   ├── GlassModal.vue
│   │   │   │   ├── GlassInput.vue
│   │   │   │   └── __tests__/   # 组件测试
│   │   │   ├── StatusCard.vue
│   │   │   ├── TimeSourceCard.vue
│   │   │   └── ApiClientDemo.vue
│   │   ├── views/           # 页面视图
│   │   ├── api/             # API 客户端层
│   │   │   ├── http-client.ts   # HTTP 客户端
│   │   │   ├── websocket-client.ts # WebSocket 客户端
│   │   │   └── __tests__/       # API 测试
│   │   ├── stores/          # Pinia 状态管理
│   │   ├── composables/     # Vue 组合式函数
│   │   ├── types/           # TypeScript 类型
│   │   └── styles/          # 样式系统
│   ├── test-results/        # 测试结果报告
│   └── dist/                # 构建输出
├── docs/                    # 📚 项目文档
│   ├── HARDWARE.md          # 硬件接口文档
│   ├── API.md               # API 接口文档
│   ├── DEVELOPMENT_GUIDE.md # 开发指南
│   └── CROSS_COMPILATION.md # 交叉编译指南
├── deployment/              # 🚀 部署配置
│   ├── nginx/               # nginx 配置
│   └── systemd/             # systemd 服务配置
├── docker/                  # 🐳 Docker 配置
├── scripts/                 # 🛠️ 构建和部署脚本
├── .github/workflows/       # 🔄 CI/CD 配置
├── clippy.toml             # Clippy 配置
├── rustfmt.toml            # Rustfmt 配置
└── .cargo/config.toml      # Cargo 配置
```

## 技术栈

### 后端技术栈
- **语言**: Rust 2021 Edition (最低版本 1.70)
- **运行时**: Tokio 异步运行时，支持高并发
- **Web 框架**: Axum 0.7，类型安全的 REST API
- **配置管理**: Figment (TOML, JSON, 环境变量)
- **时间处理**: Chrono, Time，纳秒级精度
- **错误处理**: thiserror, anyhow，结构化错误
- **日志系统**: tracing, tracing-subscriber，结构化日志
- **硬件接口**: nix, linux-embedded-hal, tokio-serial
- **数学计算**: 自实现 DSP 算法，控制理论

### 前端技术栈
- **框架**: Vue3 + TypeScript，组合式 API
- **构建工具**: Vite，快速热重载
- **状态管理**: Pinia，持久化状态
- **HTTP 客户端**: Axios，请求缓存和重试
- **WebSocket**: 原生 WebSocket，自动重连
- **UI 框架**: 自研 Glassmorphism 组件库
- **测试框架**: Vitest + Vue Test Utils
- **样式系统**: Tailwind CSS + SCSS

### 目标平台
- **主要**: Loongson 2K2000 (LoongArch64) Linux
- **开发**: macOS/Linux x86_64
- **测试**: Docker 多架构支持

## 🧠 核心算法

### 时间同步算法
- **自适应 PLL**: 动态参数调节的相位锁定环
- **FLL 算法**: 长期频率稳定性控制
- **扩展卡尔曼滤波**: 非线性系统状态估计
- **多源融合**: 基于质量评估的动态权重分配

### 信号处理
- **PPS 信号质量评估**: 实时信号质量监控
- **10MHz 频率分析**: Allan 方差和相位噪声分析
- **抖动分析**: RMS 抖动和峰峰值抖动测量
- **异常检测**: 基于统计的异常信号检测

### 故障检测与恢复
- **健康监控**: 多维度健康状态评估
- **自动故障切换**: 智能时间源选择算法
- **错误注入测试**: 完整的故障恢复验证

## 🔧 硬件接口详解

### PPS (Pulse Per Second) 接口
- **Linux PPS 子系统**: 原生 PPS 设备支持
- **设备热插拔**: 自动发现和注册 PPS 设备
- **纳秒级精度**: 高精度时间戳读取
- **信号质量监控**: 实时监控 PPS 信号质量

### 10MHz 参考时钟处理
- **频率测量**: 基于周期计数和频率计数的双重测量
- **相位检测**: 零交叉检测和相位锁定环技术
- **FLL 控制**: 频率锁定环实现长期稳定性
- **抖动分析**: Allan 方差和频率稳定性评估

### TOD (Time of Day) 串行接口
- **NMEA 协议**: 完整的 NMEA 0183 协议解析
- **GPS/北斗支持**: 多种卫星导航系统支持
- **串口通信**: 异步串口数据处理
- **数据验证**: 校验和验证和数据完整性检查

### RTC (Real Time Clock) 管理
- **DS3231 支持**: 高精度 RTC 芯片集成
- **I2C 通信**: 异步 I2C 设备操作
- **温度补偿**: 自动温度补偿和校准
- **电池备份**: 断电时间保持功能

## 快速开始

### 环境要求

- Rust 1.70+
- Node.js 18+
- npm 或 yarn

### 安装依赖

```bash
# 安装 Rust 目标平台
rustup target add loongarch64-unknown-linux-gnu
rustup target add mips64-unknown-linux-gnuabi64

# 安装前端依赖
cd frontend
npm install
cd ..
```

### 验证项目设置

```bash
./scripts/verify.sh
```

### 构建项目

```bash
# 构建所有组件
./scripts/build.sh

# 仅构建后端
cargo build --release

# 仅构建前端
cd frontend && npm run build
```

### 交叉编译

```bash
# 交叉编译到 LoongArch64
./scripts/cross-compile.sh

# 或者直接使用 Cargo
cargo build --release --target loongarch64-unknown-linux-gnu
```

### 运行开发服务器

```bash
# 启动后端 (端口 3000)
cargo run --bin server

# 启动前端开发服务器 (端口 5173)
cd frontend && npm run dev
```

## 开发工具

### 代码质量检查

```bash
# 格式化代码
cargo fmt --all

# 运行 Clippy
cargo clippy --workspace --all-targets --all-features -- -D warnings

# 运行测试
cargo test --workspace --all-targets --all-features
```

### 前端开发

```bash
cd frontend

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 运行测试
npm test

# 端到端测试
npm run test:e2e
```

## 🧪 测试与质量保证

### 后端测试架构
```bash
# 单元测试 - 核心算法测试
cargo test --workspace --all-targets --all-features

# 集成测试 - 硬件接口集成
cargo test --test integration_tests
cargo test --test error_injection_tests

# 性能基准测试
cargo bench ten_mhz_bench          # 10MHz处理器性能
cargo bench sync_algorithms_bench   # 同步算法性能
cargo bench server_benchmarks      # 服务器性能

# 硬件模拟测试
cargo test --features mock-hardware

# 故障注入测试
cargo test test_concurrent_error_scenarios
cargo test test_resource_leak_detection
cargo test test_error_recovery_mechanisms
```

### 前端测试体系
```bash
cd frontend

# 组件单元测试 - Glassmorphism组件
npm run test:unit                   # Vue组件测试
npm run test -- GlassCard          # 特定组件测试
npm run test -- GlassButton
npm run test -- ThemeToggle

# API客户端测试 - HTTP和WebSocket
npm run test:api                    # API客户端测试
npm run test -- http-client.test.ts
npm run test -- websocket-client.test.ts

# 端到端测试 - 完整用户流程
npm run test:e2e                    # Playwright E2E测试
npm run test:coverage               # 测试覆盖率报告

# 可视化测试结果
npm run test:ui                     # Vitest UI界面
```

### 测试覆盖范围

#### 后端测试覆盖
- **时间同步算法**: PLL、FLL、卡尔曼滤波器单元测试
- **硬件抽象层**: PPS、10MHz、TOD、RTC接口测试
- **服务集成**: chrony、linuxptp协调器测试
- **错误处理**: 网络错误、硬件故障、配置错误测试
- **并发安全**: 多线程访问、资源竞争测试
- **性能基准**: 纳秒级精度、吞吐量、延迟测试

#### 前端测试覆盖
- **Glassmorphism组件**: 7个核心组件完整测试
- **状态管理**: Pinia store测试
- **API集成**: HTTP客户端缓存、重试、错误处理测试
- **WebSocket**: 实时连接、重连、消息处理测试
- **无障碍性**: WCAG 2.1 AA标准合规测试
- **响应式设计**: 多设备兼容性测试

### 代码质量指标
- **测试覆盖率**: > 90% (后端), > 85% (前端)
- **Clippy 检查**: 零警告，严格模式启用
- **类型安全**: 100% TypeScript 覆盖，严格模式
- **性能基准**: 
  - API响应时间 < 10ms (P95)
  - WebSocket延迟 < 5ms
  - 时间同步精度 < 100ns
  - 前端首屏加载 < 2s

## 配置

### 后端配置

配置文件位置：
- `config.toml` (项目根目录)
- `/etc/atomic-clock-server/config.toml` (生产环境)

环境变量前缀：`ATOMIC_CLOCK_`

### 前端配置

- `frontend/vite.config.ts` - Vite 配置
- `frontend/tsconfig.json` - TypeScript 配置

## 🔌 API 文档

### REST API 端点

#### 系统管理
- `GET /api/status` - 获取系统状态和健康信息
- `GET /api/metrics` - 获取性能指标和统计数据 (支持时间范围查询)
- `POST /api/system/restart` - 重启系统服务

#### 时间源管理
- `GET /api/timesources` - 获取所有时间源状态
- `GET /api/timesources/{id}` - 获取特定时间源详情和统计
- `POST /api/timesources/{id}/start` - 启动时间源
- `POST /api/timesources/{id}/stop` - 停止时间源
- `PUT /api/timesources/{id}/config` - 更新时间源配置

#### 服务集成
- `GET /api/services` - 获取网络服务状态 (chrony/linuxptp)
- `POST /api/services/{name}/restart` - 重启网络服务
- `PUT /api/services/{name}/config` - 更新服务配置
- `GET /api/services/{name}/stats` - 获取服务统计信息

#### 配置管理
- `GET /api/config` - 获取系统配置
- `PUT /api/config` - 更新系统配置 (支持部分更新)
- `POST /api/config/reload` - 热重载配置
- `POST /api/config/validate` - 验证配置有效性

#### 告警管理
- `GET /api/alerts` - 获取告警列表 (支持分页和过滤)
- `POST /api/alerts/{id}/acknowledge` - 确认告警
- `DELETE /api/alerts/{id}` - 删除告警
- `POST /api/alerts/batch/acknowledge` - 批量确认告警

### WebSocket 实时通信

#### 数据流频道
- `/ws/realtime` - 实时系统数据推送 (1Hz更新)
- `/ws/alerts` - 告警事件通知 (实时推送)
- `/ws/metrics` - 性能指标流 (0.1Hz更新)
- `/ws/timesources` - 时间源状态变化

#### WebSocket 特性
- **自动重连**: 指数退避重连策略
- **心跳检测**: 30秒间隔心跳保活
- **消息队列**: 离线消息缓存和重发
- **状态同步**: 连接状态实时反馈

## 部署

### 生产构建

```bash
# 构建发布版本
cargo build --release --target loongarch64-unknown-linux-gnu
cd frontend && npm run build

# 部署文件
sudo cp target/loongarch64-unknown-linux-gnu/release/server /opt/atomic-clock-server/bin/
sudo cp -r frontend/dist/* /var/www/atomic-clock/
```

### 系统服务

使用 systemd 管理服务：

```bash
sudo systemctl enable atomic-clock-server
sudo systemctl start atomic-clock-server
```

## ⚠️ 注意事项

### 文件清理
项目根目录中的 `pdf.md` 文件包含 Type-C 电源切换器的电路图，与原子钟授时服务器项目无关。建议删除此文件以保持项目整洁：

```bash
# 删除无关文件
rm pdf.md
```

## 📖 详细文档

- [硬件接口文档](docs/HARDWARE.md) - 详细的硬件抽象层和接口说明
- [API 接口文档](docs/API.md) - 完整的 REST API 和 WebSocket 文档
- [开发指南](docs/DEVELOPMENT_GUIDE.md) - 开发环境设置和最佳实践
- [交叉编译指南](docs/CROSS_COMPILATION.md) - LoongArch64 交叉编译说明

## 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 或 Apache-2.0 双许可证。详见 [LICENSE](LICENSE) 文件。

## 🚀 统一管理脚本

项目提供了 `run.sh` 统一管理脚本，简化开发和部署流程：

### 快速开始

```bash
# 1. 环境检查和依赖安装
./run.sh setup

# 2. 启动开发环境 (前后端同时启动)
./run.sh dev

# 3. 构建生产版本
./run.sh build

# 4. 部署到系统 (需要 sudo 权限)
./run.sh deploy
```

### 完整命令列表

| 命令 | 功能 | 说明 |
|------|------|------|
| `./run.sh setup` | 环境检查和依赖安装 | 检查 Rust、Node.js，安装项目依赖 |
| `./run.sh dev` | 启动开发环境 | 同时启动前后端开发服务器，支持热重载 |
| `./run.sh build` | 构建生产版本 | 编译后端和前端生产版本 |
| `./run.sh deploy` | 部署到系统 | 创建系统服务并启动，配置 systemd |
| `./run.sh service status` | 查看服务状态 | 显示 systemd 服务状态 |
| `./run.sh service start` | 启动服务 | 启动系统服务 |
| `./run.sh service stop` | 停止服务 | 停止系统服务 |
| `./run.sh service restart` | 重启服务 | 重启系统服务 |
| `./run.sh service logs` | 查看日志 | 实时查看服务日志 |
| `./run.sh clean` | 清理构建文件 | 清理所有构建产物和临时文件 |
| `./run.sh help` | 显示帮助信息 | 查看所有可用命令和选项 |

### 开发环境特性

- **自动配置**: 自动创建开发环境配置文件
- **热重载**: 支持前后端代码热重载
- **依赖检查**: 自动检查和安装必要依赖
- **服务监控**: 实时监控服务状态
- **错误处理**: 完善的错误处理和恢复机制

### 生产部署特性

- **一键部署**: 自动构建、配置和启动服务
- **系统集成**: 自动创建 systemd 服务
- **权限管理**: 自动创建系统用户和权限
- **配置管理**: 自动生成生产环境配置
- **服务管理**: 完整的服务生命周期管理

### 使用示例

```bash
# 开发流程
./run.sh setup          # 初始化环境
./run.sh dev             # 开发调试
# 访问 http://localhost:5173 查看前端
# 访问 http://localhost:3000/api/docs 查看 API

# 生产部署
./run.sh build           # 构建生产版本
./run.sh deploy          # 部署到系统

# 服务管理
./run.sh service status  # 查看状态
./run.sh service logs    # 查看日志
./run.sh service restart # 重启服务
```

## 联系方式

- 项目主页: https://github.com/atomic-clock-server/atomic-clock-server
- 文档: https://github.com/atomic-clock-server/docs
- 问题反馈: https://github.com/atomic-clock-server/atomic-clock-server/issues